/**
 * <PERSON><PERSON><PERSON> to manually confirm unverified users in Supabase
 * Run this script to confirm existing users who were created before autoconfirm was enabled
 */

const { createClient } = require('@supabase/supabase-js')

// You'll need to add your service role key to .env.local
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  console.error('Make sure you have NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env.local file')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function confirmAllUsers() {
  try {
    console.log('Fetching unverified users...')
    
    // Get all users
    const { data: users, error } = await supabase.auth.admin.listUsers()
    
    if (error) {
      console.error('Error fetching users:', error)
      return
    }

    console.log(`Found ${users.users.length} total users`)

    // Filter unverified users
    const unverifiedUsers = users.users.filter(user => !user.email_confirmed_at)
    
    console.log(`Found ${unverifiedUsers.length} unverified users`)

    if (unverifiedUsers.length === 0) {
      console.log('All users are already verified!')
      return
    }

    // Confirm each unverified user
    for (const user of unverifiedUsers) {
      console.log(`Confirming user: ${user.email}`)
      
      const { error: confirmError } = await supabase.auth.admin.updateUserById(
        user.id,
        { email_confirm: true }
      )
      
      if (confirmError) {
        console.error(`Error confirming user ${user.email}:`, confirmError)
      } else {
        console.log(`✅ Successfully confirmed user: ${user.email}`)
      }
    }

    console.log('✅ All users have been processed!')
    
  } catch (error) {
    console.error('Unexpected error:', error)
  }
}

// Run the script
confirmAllUsers()
