/**
 * Simple script to clear local storage data
 * Run this in the browser console if you're having issues with cached data
 */

// Clear all local storage
localStorage.clear()

// Clear all session storage
sessionStorage.clear()

// Clear specific items that might be cached
const itemsToRemove = [
  'user',
  'progressData', 
  'workoutData',
  'supabase.auth.token',
  'sb-nusyycnumaueiocpisap-auth-token'
]

itemsToRemove.forEach(item => {
  localStorage.removeItem(item)
  sessionStorage.removeItem(item)
})

console.log('✅ Storage cleared! Refresh the page to start fresh.')
