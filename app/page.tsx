"use client"

import { useState, useEffect } from "react"
import { SignupForm } from "@/components/signup-form"
import { LoginForm } from "@/components/login-form"
import Questionnaire from "@/components/questionnaire"
import { Dashboard } from "@/components/dashboard"
import { AdminReset } from "@/components/admin-reset"
import { ErrorAlert } from "@/components/ui/error-alert"
import { SignupFormData } from "@/lib/types"
import { AppErrorHandler } from "@/lib/error-handler"
import { useUser, useError } from "@/lib/app-context"
import { useAuthContext } from "@/lib/auth-context"
import { DataService } from "@/lib/data-service"
import { IntakeService } from "@/lib/intake-service"

export default function Home() {
  const { user, setUser, currentStep, setCurrentStep } = useUser()
  const { errorState, setError, clearError } = useError()
  const { user: authUser, loading: authLoading, signOut } = useAuthContext()
  const [showLogin, setShowLogin] = useState(true)

  // Check for testing bypass
  const isTestMode = typeof window !== 'undefined' && window.location.search.includes('test=true')



  // Effect to handle auth user changes - MUST be before any conditional returns
  useEffect(() => {
    // Wait for auth to finish loading
    if (authLoading) {
      return
    }

    // If no authenticated user, ensure we're on signup
    if (!authUser) {
      if (currentStep !== "signup") {
        setCurrentStep("signup")
      }
      return
    }

    // If we have an authenticated user but no app user data, check intake completion
    if (authUser && !user) {
      const checkIntakeCompletion = async () => {
        try {

          const fullName = authUser.user_metadata?.full_name || authUser.user_metadata?.name || ''
          const nameParts = fullName.split(' ')
          const userData = {
            firstName: authUser.user_metadata?.first_name || nameParts[0] || '',
            lastName: authUser.user_metadata?.last_name || nameParts.slice(1).join(' ') || '',
            email: authUser.email,
            id: authUser.id,
            name: fullName,
            program: authUser.user_metadata?.program || null
          }
          setUser(userData)

          // Testing bypass - skip questionnaire if test=true in URL
          if (isTestMode) {
            userData.program = "Test Program"
            setUser(userData)
            setCurrentStep("dashboard")
            return
          }

          // Check if user has completed intake in database
          const hasCompleted = await IntakeService.hasCompletedIntake(authUser.id)

          if (hasCompleted) {
            // Get their latest intake response to get program name
            const { data: intakeData } = await IntakeService.getLatestIntakeResponse(authUser.id)
            if (intakeData) {
              userData.program = intakeData.program_name
              setUser(userData)
            }
            setCurrentStep("dashboard")
          } else {
            setCurrentStep("questionnaire")
          }
        } catch (error) {
          console.error('Error checking intake completion:', error)
          setCurrentStep("questionnaire") // Fallback to questionnaire
        }
      }

      checkIntakeCompletion()
    }
  }, [authLoading, authUser, user, isTestMode]) // Simplified dependencies

  const handleSignupComplete = (userData: SignupFormData) => {
    try {
      // Create user object with auth user data
      const newUser = {
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        id: authUser?.id, // Include Supabase user ID
        name: `${userData.firstName} ${userData.lastName}`,
        // Don't store password in user state for security
      }
      setUser(newUser)
      setCurrentStep("questionnaire")
      clearError()
    } catch (error) {
      const appError = AppErrorHandler.handleStorageError(error)
      setError({ hasError: true, error: appError })
    }
  }

  const handleQuestionnaireComplete = async (program_id: string, program_name: string, answers: Record<string, any>) => {
    try {
      console.log('🎯 Questionnaire completion - showing result screen:', { program_id, program_name, answers, user: user?.id, authUser: authUser?.id })

      if (!user || !authUser) {
        console.error('❌ Missing user data:', { user: !!user, authUser: !!authUser })
        const appError = AppErrorHandler.createError(
          "User data is missing. Please start over by signing up again.",
          "general"
        )
        setError({ hasError: true, error: appError })
        setCurrentStep("signup")
        return
      }

      // Save intake responses to database when "Get Started" is clicked
      console.log('💾 Saving intake response to database...')
      const { data: intakeData, error: intakeError } = await IntakeService.saveIntakeResponse(
        authUser.id,
        answers,
        program_id,
        program_name
      )

      if (intakeError) {
        console.error('❌ Error saving intake response:', intakeError)
        const appError = AppErrorHandler.createError(
          "Failed to save your responses. Please try again.",
          "general"
        )
        setError({ hasError: true, error: appError })
        return
      }

      console.log('✅ Intake response saved successfully:', intakeData)

      // Update user metadata in Supabase to save the program (for backward compatibility)
      console.log('🔄 Updating user metadata...')
      const { supabase } = await import('@/lib/supabase')
      await supabase.auth.updateUser({
        data: {
          ...authUser.user_metadata,
          program: program_name
        }
      })

      const updatedUser = { ...user, program: program_name }
      setUser(updatedUser)
      console.log('🎉 Get Started clicked - redirecting to dashboard')
      setCurrentStep("dashboard")
      clearError()
    } catch (error) {
      const appError = AppErrorHandler.handleStorageError(error)
      setError({ hasError: true, error: appError })
    }
  }

  const handleErrorDismiss = () => {
    clearError()
  }

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 via-red-50 to-pink-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Loading...</h2>
          <p className="text-gray-600">Setting up your fitness journey</p>
          <div className="mt-4 text-xs text-gray-500">
            Auth Loading: {authLoading ? 'true' : 'false'}<br/>
            Current Step: {currentStep}<br/>
            Auth User: {authUser ? 'exists' : 'none'}<br/>
            App User: {user ? 'exists' : 'none'}
          </div>
        </div>
      </div>
    )
  }



  if (currentStep === "signup") {
    return (
      <div>
        {errorState.hasError && errorState.error && (
          <div className="fixed top-4 left-4 right-4 z-50">
            <ErrorAlert error={errorState.error} onDismiss={handleErrorDismiss} />
          </div>
        )}
        {showLogin ? (
          <div className="min-h-screen bg-gradient-to-br from-orange-50 via-red-50 to-pink-50 flex items-center justify-center p-4">
            <div className="w-full max-w-md">
              {/* First Time User Message */}
              <div className="text-center mb-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
                <h2 className="text-lg font-semibold text-blue-900 mb-2">
                  🎯 First Time Accessing Your Program?
                </h2>
                <p className="text-blue-700 text-sm mb-3">
                  If this is your first visit, you'll need to create an account to get started with your personalized fitness journey.
                </p>
                <button
                  onClick={() => setShowLogin(false)}
                  className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold py-2 px-6 rounded-lg transition-all duration-300"
                >
                  Sign Up Here
                </button>
              </div>

              {/* Login Form */}
              <LoginForm onSwitchToSignup={() => setShowLogin(false)} />
            </div>
          </div>
        ) : (
          <SignupForm
            onComplete={handleSignupComplete}
            onSwitchToLogin={() => setShowLogin(true)}
          />
        )}
      </div>
    )
  }

  if (currentStep === "questionnaire") {
    return (
      <div>
        {errorState.hasError && errorState.error && (
          <div className="fixed top-4 left-4 right-4 z-50">
            <ErrorAlert error={errorState.error} onDismiss={handleErrorDismiss} />
          </div>
        )}
        <Questionnaire onComplete={handleQuestionnaireComplete} />
      </div>
    )
  }

  if (currentStep === "dashboard" && user) {
    return (
      <div>
        {errorState.hasError && errorState.error && (
          <div className="fixed top-4 left-4 right-4 z-50">
            <ErrorAlert error={errorState.error} onDismiss={handleErrorDismiss} />
          </div>
        )}
        <Dashboard user={user} />
        <AdminReset />
      </div>
    )
  }

  // Fallback - should not reach here normally
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Loading...</h2>
        <p className="text-gray-600">Setting up your fitness journey</p>
      </div>
    </div>
  )
}
