import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import "./globals.css"
import { AppProvider } from "@/lib/app-context"
import { AuthProvider } from "@/lib/auth-context"

export const metadata: Metadata = {
  title: "Bars & Barbells Fit",
  description: "Your personal fitness companion for strength training and wellness",
  generator: "v0.dev",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <AuthProvider>
          <AppProvider>
            {children}
          </AppProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
