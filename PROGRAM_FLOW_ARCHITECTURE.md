# Program Assignment and Display Flow Architecture

## Complete Technical Flow After "Get Started" Click

### **Phase 1: Questionnaire Completion → Database Save**

#### 1a. User Clicks "Get Started" Button
**Location**: `components/questionnaire.tsx:168-180`
```typescript
const handleGetStarted = () => {
  if (matchedProgram) {
    const mobility_add_on = getMobilityAddOn()
    const finalExperience = answers.has_limitations ? 'beginner' : answers.experience
    const finalAnswers = { 
      ...answers, 
      experience: finalExperience,
      mobility_add_on 
    }
    onComplete(matchedProgram.id, matchedProgram.name, finalAnswers)
  }
}
```

#### 1b. Data Flows to Parent Component
**Location**: `app/page.tsx:112-167`
```typescript
const handleQuestionnaireComplete = async (program_id: string, program_name: string, answers: Record<string, any>) => {
  // Save to intake_responses table
  const { data: intakeData, error: intakeError } = await IntakeService.saveIntakeResponse(
    authUser.id,
    answers,
    program_id,
    program_name
  )
  
  // Update user metadata (backward compatibility)
  await supabase.auth.updateUser({
    data: { ...authUser.user_metadata, program: program_name }
  })
  
  // Update app state
  const updatedUser = { ...user, program: program_name }
  setUser(updatedUser)
  setCurrentStep("dashboard")
}
```

#### 1c. Database Save Operation
**Location**: `lib/intake-service.ts:24-56`
```typescript
static async saveIntakeResponse(userId, answers, programId, programName) {
  const intakeData = {
    user_id: userId,
    has_limitations: answers.has_limitations || false,
    location: answers.location,
    goal: answers.goal,
    experience: answers.experience,
    wants_mobility: answers.wants_mobility || false,
    flex_score: answers.flex_score || null,
    mobility_add_on: answers.mobility_add_on || 'none',
    program_id: programId,        // e.g., "FL_GYM_BEG"
    program_name: programName,    // e.g., "Fat Loss • Gym • Beginner"
    completion_status: 'complete'
  }
  
  return await supabase.from('intake_responses').insert(intakeData)
}
```

### **Phase 2: Dashboard Load → Program Display**

#### 2a. Dashboard Component Initialization
**Location**: `components/dashboard.tsx:205-207`
```typescript
<TabsContent value="workout" className="space-y-6">
  <WorkoutProgram program={user.program ?? ""} />
</TabsContent>
```

#### 2b. User State Management
**Location**: `app/page.tsx:68-78`
```typescript
// On app load, check if user completed intake
const hasCompleted = await IntakeService.hasCompletedIntake(authUser.id)

if (hasCompleted) {
  // Get their latest intake response to get program name
  const { data: intakeData } = await IntakeService.getLatestIntakeResponse(authUser.id)
  if (intakeData) {
    userData.program = intakeData.program_name  // Sets program in user state
    setUser(userData)
  }
  setCurrentStep("dashboard")
}
```

#### 2c. Program Display in Multiple Locations
**Locations where program is displayed:**

1. **Header Avatar Section** (`components/dashboard.tsx:101`)
```typescript
{user.program && <p className="text-xs text-gray-500">{user.program}</p>}
```

2. **Welcome Banner** (`components/dashboard.tsx:146-150`)
```typescript
{user.program && (
  <div className="inline-flex items-center rounded-full bg-white/20 px-3 py-2">
    <span>Current Program: {user.program}</span>
  </div>
)}
```

3. **Workout Tab Content** (`components/dashboard.tsx:206`)
```typescript
<WorkoutProgram program={user.program ?? ""} />
```

### **Phase 3: Workout Program Component**

#### 3a. Program Matching Logic
**Location**: `components/workout-program.tsx:16-262`
```typescript
const workoutPrograms = {
  "Beginner Fat Loss": { /* workout details */ },
  "Beginner Muscle Building": { /* workout details */ },
  // ... other programs
}

export default function WorkoutProgram({ program }: { program: string }) {
  const selectedProgram = workoutPrograms[program as keyof typeof workoutPrograms]
  
  if (!selectedProgram) {
    return <div>Program not found: {program}</div>
  }
  
  // Render workout details
}
```

## **Data Flow Summary**

```
1. Questionnaire Answers
   ↓
2. Program Lookup (FL_GYM_BEG → "Fat Loss • Gym • Beginner")
   ↓
3. Database Save (intake_responses table)
   ↓
4. User State Update (user.program = "Fat Loss • Gym • Beginner")
   ↓
5. Dashboard Render
   ↓
6. WorkoutProgram Component Receives program prop
   ↓
7. Program Content Display (if exists in workoutPrograms object)
```

## **State Management Architecture**

### **Global User State**
**Location**: `lib/app-context.tsx` (inferred from usage)
```typescript
interface User {
  firstName: string
  lastName: string
  email: string
  id: string
  program?: string  // This holds the program name
}
```

### **State Flow**
1. **Initial Load**: `app/page.tsx` checks `IntakeService.hasCompletedIntake()`
2. **Program Retrieval**: `IntakeService.getLatestIntakeResponse()` gets program_name
3. **State Update**: `setUser({ ...userData, program: intakeData.program_name })`
4. **Component Prop**: Dashboard passes `user.program` to WorkoutProgram
5. **Display**: WorkoutProgram matches program name to content

## **Critical Architecture Notes**

### **Dual Storage System**
The system uses **dual storage** for backward compatibility:
1. **Primary**: `intake_responses.program_name` (database)
2. **Secondary**: `auth.user_metadata.program` (Supabase Auth)

### **Program ID vs Program Name**
- **program_id**: Short identifier (`FL_GYM_BEG`) - stored but not currently used
- **program_name**: Display name (`Fat Loss • Gym • Beginner`) - used for UI display

### **Missing Program Content Issue**
**Current Gap**: The `workoutPrograms` object in `workout-program.tsx` uses hardcoded keys like "Beginner Fat Loss" but the questionnaire generates names like "Fat Loss • Gym • Beginner".

**This is why programs may not display correctly** - there's a mismatch between:
- Generated program names: `"Fat Loss • Gym • Beginner"`
- Hardcoded program keys: `"Beginner Fat Loss"`

## **Components Involved in Program Flow**

1. **`components/questionnaire.tsx`** - Generates program assignment
2. **`app/page.tsx`** - Handles state management and database operations
3. **`lib/intake-service.ts`** - Database CRUD operations
4. **`components/dashboard.tsx`** - Displays program in multiple locations
5. **`components/workout-program.tsx`** - Renders actual workout content
6. **`lib/app-context.tsx`** - Global state management (inferred)

## **Next Steps for Program Content**
When you add actual program content, you'll need to:
1. **Update program keys** in `workoutPrograms` to match generated names
2. **Create program content** for all 16 base programs from the lookup table
3. **Add program_id based lookup** as alternative to program_name matching
