# Database Migration Instructions

## Updating Intake Responses Schema

This migration updates the `intake_responses` table to match the new questionnaire structure from the implementation spec.

### Before Running Migration

1. **Backup existing data** (if you have important data):
   ```sql
   CREATE TABLE intake_responses_backup AS SELECT * FROM intake_responses;
   ```

2. **Verify current table structure**:
   ```sql
   \d intake_responses
   ```

### Running the Migration

1. Open your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of `update_intake_responses_schema.sql`
4. Execute the migration

### What This Migration Does

#### Old Schema (Removed):
- `goals` (text array)
- `equipment` (text)
- `frequency` (text)
- `mobility_concerns` (text array)

#### New Schema (Added):
- `has_limitations` (boolean) - Whether user has serious injuries/restrictions
- `location` (text) - Where user will workout and equipment access
- `goal` (text) - Single primary goal (not array)
- `experience` (text) - Training experience level
- `wants_mobility` (boolean) - Whether user wants mobility routine
- `flex_score` (integer) - Flexibility rating 1-10 (optional)
- `mobility_add_on` (text) - Type of mobility add-on assigned
- `program_id` (text) - Short program identifier (e.g., FL_GYM_BEG)

#### Constraints Added:
- `location` must be one of: 'home_no_equipment', 'home_willing_to_purchase', 'home_with_equipment', 'gym'
- `goal` must be one of: 'fat_loss', 'build_strength', 'healthy_habits'
- `experience` must be one of: 'beginner', 'intermediate', 'advanced'
- `flex_score` must be between 1 and 10 (if provided)
- `mobility_add_on` must be one of: 'none', 'beginner', 'advanced'

### After Migration

1. **Verify the new structure**:
   ```sql
   \d intake_responses
   ```

2. **Test with sample data**:
   ```sql
   SELECT * FROM intake_responses LIMIT 5;
   ```

3. **Update your application** to use the new IntakeService methods

### Rollback (if needed)

If you need to rollback:

1. **Restore from backup**:
   ```sql
   DROP TABLE intake_responses;
   ALTER TABLE intake_responses_backup RENAME TO intake_responses;
   ```

2. **Revert application code** to use old IntakeService interface

### Notes

- This migration will **drop all existing data** in the intake_responses table
- Make sure to backup important data before running
- The new schema enforces data integrity with CHECK constraints
- Row Level Security (RLS) policies are automatically created
- Indexes are added for better query performance
