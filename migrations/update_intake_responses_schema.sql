-- Migration to update intake_responses table for new questionnaire structure
-- Run this in your Supabase SQL editor

-- First, backup existing data if needed
-- CREATE TABLE intake_responses_backup AS SELECT * FROM intake_responses;

-- Drop the existing table and recreate with new structure
DROP TABLE IF EXISTS intake_responses;

-- Create the new intake_responses table with updated schema
CREATE TABLE intake_responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  has_limitations BOOLEAN NOT NULL DEFAULT false,
  location TEXT NOT NULL CHECK (location IN ('home_no_equipment', 'home_willing_to_purchase', 'home_with_equipment', 'gym')),
  goal TEXT NOT NULL CHECK (goal IN ('fat_loss', 'build_strength', 'healthy_habits')),
  experience TEXT NOT NULL CHECK (experience IN ('beginner', 'intermediate', 'advanced')),
  wants_mobility BOOLEAN NOT NULL DEFAULT false,
  flex_score INTEGER CHECK (flex_score >= 1 AND flex_score <= 10),
  mobility_add_on TEXT NOT NULL DEFAULT 'none' CHECK (mobility_add_on IN ('none', 'beginner', 'advanced')),
  program_id TEXT NOT NULL,
  program_name TEXT NOT NULL,
  completion_status TEXT NOT NULL DEFAULT 'complete' CHECK (completion_status IN ('complete', 'incomplete')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_intake_responses_user_id ON intake_responses(user_id);
CREATE INDEX idx_intake_responses_completion_status ON intake_responses(completion_status);
CREATE INDEX idx_intake_responses_created_at ON intake_responses(created_at);

-- Enable Row Level Security
ALTER TABLE intake_responses ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own intake responses" ON intake_responses
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own intake responses" ON intake_responses
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own intake responses" ON intake_responses
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own intake responses" ON intake_responses
  FOR DELETE USING (auth.uid() = user_id);

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_intake_responses_updated_at
  BEFORE UPDATE ON intake_responses
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT ALL ON intake_responses TO authenticated;
GRANT ALL ON intake_responses TO service_role;

-- Optional: Insert some test data to verify the schema works
-- INSERT INTO intake_responses (
--   user_id,
--   has_limitations,
--   location,
--   goal,
--   experience,
--   wants_mobility,
--   flex_score,
--   mobility_add_on,
--   program_id,
--   program_name
-- ) VALUES (
--   auth.uid(),
--   false,
--   'gym',
--   'fat_loss',
--   'beginner',
--   true,
--   5,
--   'beginner',
--   'FL_GYM_BEG',
--   'Fat Loss • Gym • Beginner'
-- );

COMMENT ON TABLE intake_responses IS 'Stores user responses from the program intake questionnaire';
COMMENT ON COLUMN intake_responses.has_limitations IS 'Whether user has serious injuries or mobility restrictions';
COMMENT ON COLUMN intake_responses.location IS 'Where user will workout and equipment access';
COMMENT ON COLUMN intake_responses.goal IS 'Users primary fitness goal';
COMMENT ON COLUMN intake_responses.experience IS 'Users training experience level';
COMMENT ON COLUMN intake_responses.wants_mobility IS 'Whether user wants mobility routine added';
COMMENT ON COLUMN intake_responses.flex_score IS 'Users flexibility rating from 1-10 (only if wants_mobility=true)';
COMMENT ON COLUMN intake_responses.mobility_add_on IS 'Type of mobility add-on assigned (none, beginner, advanced)';
COMMENT ON COLUMN intake_responses.program_id IS 'Short program identifier (e.g., FL_GYM_BEG)';
COMMENT ON COLUMN intake_responses.program_name IS 'Full program name (e.g., Fat Loss • Gym • Beginner)';
