"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { ExternalLink, ShoppingCart } from "lucide-react"

export function Recommendations() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <Card className="bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 text-white border-0 shadow-2xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-3xl font-bold mb-2 flex items-center">
                <ShoppingCart className="h-8 w-8 mr-3" />
                Recommendations
              </CardTitle>
              <CardDescription className="text-white/90 text-lg">
                Quality equipment, supplements, and tools to support your fitness journey
              </CardDescription>
            </div>
            <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm">
              <span className="text-2xl">🛒</span>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="space-y-8">
        
        {/* Equipment Section */}
        <div className="border border-gray-200 rounded-xl p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-3">
            <span className="text-2xl">🏋️</span>
            Fitness Equipment
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            {/* Resistance Bands */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-blue-800">Resistance Bands Set</h4>
                <ExternalLink className="h-4 w-4 text-blue-600" />
              </div>
              <p className="text-blue-700 text-sm mb-3">
                Perfect for home workouts and travel. Multiple resistance levels for all fitness levels.
              </p>
              <a 
                href="#" 
                className="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                View on Amazon
              </a>
            </div>

            {/* Adjustable Dumbbells */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-green-800">Adjustable Dumbbells</h4>
                <ExternalLink className="h-4 w-4 text-green-600" />
              </div>
              <p className="text-green-700 text-sm mb-3">
                Space-saving solution for strength training at home. Replaces multiple sets of weights.
              </p>
              <a 
                href="#" 
                className="inline-block bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                View on Amazon
              </a>
            </div>

            {/* Yoga Mat */}
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-purple-800">Premium Yoga Mat</h4>
                <ExternalLink className="h-4 w-4 text-purple-600" />
              </div>
              <p className="text-purple-700 text-sm mb-3">
                Non-slip, eco-friendly mat for yoga, stretching, and floor exercises.
              </p>
              <a 
                href="#" 
                className="inline-block bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                View on Amazon
              </a>
            </div>
          </div>
        </div>

        {/* Supplements Section */}
        <div className="border border-gray-200 rounded-xl p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-3">
            <span className="text-2xl">💊</span>
            Supplements
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            {/* Protein Powder */}
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-orange-800">Whey Protein Powder</h4>
                <ExternalLink className="h-4 w-4 text-orange-600" />
              </div>
              <p className="text-orange-700 text-sm mb-3">
                High-quality protein for muscle recovery and growth. Great for post-workout nutrition.
              </p>
              <a 
                href="#" 
                className="inline-block bg-orange-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-orange-700 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                View on Amazon
              </a>
            </div>

            {/* Omega-3 */}
            <div className="bg-teal-50 border border-teal-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-teal-800">Omega-3 Fish Oil</h4>
                <ExternalLink className="h-4 w-4 text-teal-600" />
              </div>
              <p className="text-teal-700 text-sm mb-3">
                Essential fatty acids for heart health, brain function, and reducing inflammation.
              </p>
              <a 
                href="#" 
                className="inline-block bg-teal-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-teal-700 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                View on Amazon
              </a>
            </div>

            {/* Multivitamin */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-red-800">Daily Multivitamin</h4>
                <ExternalLink className="h-4 w-4 text-red-600" />
              </div>
              <p className="text-red-700 text-sm mb-3">
                Comprehensive vitamin and mineral support to fill nutritional gaps in your diet.
              </p>
              <a 
                href="#" 
                className="inline-block bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                View on Amazon
              </a>
            </div>
          </div>
        </div>

        {/* Kitchen Tools Section */}
        <div className="border border-gray-200 rounded-xl p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-3">
            <span className="text-2xl">🍳</span>
            Kitchen Tools
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            {/* Food Scale */}
            <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-indigo-800">Digital Food Scale</h4>
                <ExternalLink className="h-4 w-4 text-indigo-600" />
              </div>
              <p className="text-indigo-700 text-sm mb-3">
                Accurate portion control and macro tracking. Essential for precise nutrition.
              </p>
              <a 
                href="#" 
                className="inline-block bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                View on Amazon
              </a>
            </div>

            {/* Meal Prep Containers */}
            <div className="bg-pink-50 border border-pink-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-pink-800">Meal Prep Containers</h4>
                <ExternalLink className="h-4 w-4 text-pink-600" />
              </div>
              <p className="text-pink-700 text-sm mb-3">
                BPA-free containers for meal planning and portion control. Microwave and dishwasher safe.
              </p>
              <a 
                href="#" 
                className="inline-block bg-pink-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-pink-700 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                View on Amazon
              </a>
            </div>

            {/* Blender */}
            <div className="bg-cyan-50 border border-cyan-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-cyan-800">High-Speed Blender</h4>
                <ExternalLink className="h-4 w-4 text-cyan-600" />
              </div>
              <p className="text-cyan-700 text-sm mb-3">
                Perfect for protein shakes, smoothies, and healthy meal preparation.
              </p>
              <a 
                href="#" 
                className="inline-block bg-cyan-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-cyan-700 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                View on Amazon
              </a>
            </div>
          </div>
        </div>

        {/* Disclaimer */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <p className="text-gray-600 text-sm">
            <strong>Disclaimer:</strong> These are affiliate links. When you purchase through these links, we may earn a small commission at no additional cost to you. We only recommend products we believe will genuinely help you on your fitness journey.
          </p>
        </div>

      </div>
    </div>
  )
}
