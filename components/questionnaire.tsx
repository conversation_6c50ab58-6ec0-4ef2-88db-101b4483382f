"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { ChevronRight, ChevronLeft, CheckCircle, Target, User, Heart, Home, RotateCcw } from "lucide-react"

interface QuestionnaireProps {
  onComplete: (program_id: string, program_name: string, answers: Record<string, any>) => void
}

// Program lookup table based on spec
const PROGRAM_LOOKUP = {
  // Home no equipment
  "home_no_equipment_fat_loss_beginner": { name: "Fat Loss • Bodyweight • Beginner", id: "FL_BW_BEG" },
  "home_no_equipment_fat_loss_intermediate": { name: "Fat Loss • Bodyweight • Intermediate", id: "FL_BW_INT" },
  "home_no_equipment_healthy_habits_beginner": { name: "Healthy Habits • Bodyweight • Beginner", id: "HH_BW_BEG" },
  "home_no_equipment_healthy_habits_intermediate": { name: "Healthy Habits • Bodyweight • Intermediate", id: "HH_BW_INT" },
  
  // Home willing to purchase
  "home_willing_to_purchase_fat_loss_beginner": { name: "Fat Loss • Home Equip • Beginner", id: "FL_HOME_BEG" },
  "home_willing_to_purchase_fat_loss_intermediate": { name: "Fat Loss • Home Equip • Intermediate", id: "FL_HOME_INT" },
  "home_willing_to_purchase_build_strength_beginner": { name: "Strength • Home Equip • Beginner", id: "ST_HOME_BEG" },
  "home_willing_to_purchase_build_strength_intermediate": { name: "Strength • Home Equip • Intermediate", id: "ST_HOME_INT" },
  "home_willing_to_purchase_healthy_habits_beginner": { name: "Healthy Habits • Home Equip • Beginner", id: "HH_HOME_BEG" },
  "home_willing_to_purchase_healthy_habits_intermediate": { name: "Healthy Habits • Home Equip • Intermediate", id: "HH_HOME_INT" },
  
  // Home with equipment
  "home_with_equipment_fat_loss_beginner": { name: "Fat Loss • Home Equip • Beginner", id: "FL_HOME_BEG" },
  "home_with_equipment_fat_loss_intermediate": { name: "Fat Loss • Home Equip • Intermediate", id: "FL_HOME_INT" },
  "home_with_equipment_build_strength_beginner": { name: "Strength • Home Equip • Beginner", id: "ST_HOME_BEG" },
  "home_with_equipment_build_strength_intermediate": { name: "Strength • Home Equip • Intermediate", id: "ST_HOME_INT" },
  "home_with_equipment_healthy_habits_beginner": { name: "Healthy Habits • Home Equip • Beginner", id: "HH_HOME_BEG" },
  "home_with_equipment_healthy_habits_intermediate": { name: "Healthy Habits • Home Equip • Intermediate", id: "HH_HOME_INT" },
  
  // Gym
  "gym_fat_loss_beginner": { name: "Fat Loss • Gym • Beginner", id: "FL_GYM_BEG" },
  "gym_fat_loss_intermediate": { name: "Fat Loss • Gym • Intermediate", id: "FL_GYM_INT" },
  "gym_fat_loss_advanced": { name: "Fat Loss • Gym • Advanced", id: "FL_GYM_ADV" },
  "gym_build_strength_beginner": { name: "Strength • Gym • Beginner", id: "ST_GYM_BEG" },
  "gym_build_strength_intermediate": { name: "Strength • Gym • Intermediate", id: "ST_GYM_INT" },
  "gym_build_strength_advanced": { name: "Strength • Gym • Advanced", id: "ST_GYM_ADV" },
}

export default function Questionnaire({ onComplete }: QuestionnaireProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [answers, setAnswers] = useState<Record<string, any>>({})
  const [showResult, setShowResult] = useState(false)
  const [matchedProgram, setMatchedProgram] = useState<{ name: string; id: string } | null>(null)

  // Question flow based on spec
  const getQuestionFlow = () => {
    const flow = ["has_limitations"]
    flow.push("location")
    flow.push("goal")
    flow.push("experience")
    flow.push("wants_mobility")
    
    if (answers.wants_mobility === true) {
      flow.push("flex_score")
    }
    
    return flow
  }

  const questionFlow = getQuestionFlow()
  const currentQuestionId = questionFlow[currentStep]

  // Get location options based on limitations
  const getLocationOptions = () => {
    const options = [
      { value: "home_no_equipment", label: "Home, bodyweight only", description: "No equipment needed" },
      { value: "home_willing_to_purchase", label: "Home, willing to buy basics", description: "Ready to invest in basic equipment" },
      { value: "home_with_equipment", label: "Home, already have equipment", description: "Dumbbells, bands, etc." },
    ]
    
    if (!answers.has_limitations) {
      options.push({ value: "gym", label: "Gym access", description: "Full gym membership" })
    }
    
    return options
  }

  // Get goal options based on location
  const getGoalOptions = () => {
    if (answers.location === "home_no_equipment") {
      return [
        { value: "fat_loss", label: "Fat Loss", description: "Burn fat, improve health & feel more confident" },
        { value: "healthy_habits", label: "Create Healthy Habits", description: "Build sustainable routines for longevity, improve energy, decrease pain and move your body more" },
      ]
    } else if (answers.location === "gym") {
      return [
        { value: "fat_loss", label: "Fat Loss", description: "Burn fat, improve health & feel more confident" },
        { value: "build_strength", label: "Build Strength", description: "Build muscle + strength, tone up & reduce aches & pains" },
      ]
    } else {
      return [
        { value: "fat_loss", label: "Fat Loss", description: "Burn fat, improve health & feel more confident" },
        { value: "build_strength", label: "Build Strength", description: "Build muscle + strength, tone up & reduce aches & pains" },
        { value: "healthy_habits", label: "Create Healthy Habits", description: "Build sustainable routines for longevity, improve energy, decrease pain and move your body more" },
      ]
    }
  }

  // Get experience options based on location
  const getExperienceOptions = () => {
    if (answers.location === "gym") {
      return [
        { value: "beginner", label: "Beginner", description: "New to fitness or returning after time off, 30 min workouts & perfect for building consistency" },
        { value: "intermediate", label: "Intermediate", description: "Some training experience, 45-60 min workouts & wants a challenge" },
        { value: "advanced", label: "Advanced", description: "1+ years consistent training, 60+ min workouts & looking to push yourself" },
      ]
    } else {
      return [
        { value: "beginner", label: "Beginner", description: "New to fitness or returning after time off, 30 min workouts & perfect for building consistency" },
        { value: "intermediate", label: "Intermediate", description: "Some training experience, 45-60 min workouts & wants a challenge" },
      ]
    }
  }

  // Program lookup function with auto-experience assignment
  const lookupProgram = (location: string, goal: string, experience: string, has_limitations: boolean) => {
    // Auto-assign beginner experience if user has limitations
    const finalExperience = has_limitations ? 'beginner' : experience
    const key = `${location}_${goal}_${finalExperience}`
    return PROGRAM_LOOKUP[key as keyof typeof PROGRAM_LOOKUP] || null
  }

  // Determine mobility add-on
  const getMobilityAddOn = () => {
    if (!answers.wants_mobility) return "none"
    return answers.flex_score >= 7 ? "advanced" : "beginner"
  }

  const handleAnswer = (value: any) => {
    setAnswers({ ...answers, [currentQuestionId]: value })
  }

  const handleNext = () => {
    if (currentStep < questionFlow.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      // Complete questionnaire
      const program = lookupProgram(answers.location, answers.goal, answers.experience, answers.has_limitations)
      if (program) {
        setMatchedProgram(program)
        setShowResult(true)
      }
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleRetake = () => {
    setCurrentStep(0)
    setAnswers({})
    setShowResult(false)
    setMatchedProgram(null)
  }

  const handleGetStarted = () => {
    if (matchedProgram) {
      const mobility_add_on = getMobilityAddOn()
      // Apply auto-experience assignment for final answers
      const finalExperience = answers.has_limitations ? 'beginner' : answers.experience
      const finalAnswers = {
        ...answers,
        experience: finalExperience,
        mobility_add_on
      }
      onComplete(matchedProgram.id, matchedProgram.name, finalAnswers)
    }
  }

  const isAnswered = () => {
    return answers[currentQuestionId] !== undefined && answers[currentQuestionId] !== null
  }

  const progress = ((currentStep + 1) / questionFlow.length) * 100

  // Get current question data
  const getCurrentQuestion = () => {
    switch (currentQuestionId) {
      case "has_limitations":
        return {
          title: "Do you have any serious injuries or mobility restrictions that would limit your workouts? For example: hip or knee replacement, torn ligaments, hernia, trouble standing up or sitting down without help, osteoporosis",
          icon: Heart,
          options: [
            { value: true, label: "Yes", description: "I have serious injuries or restrictions to consider" },
            { value: false, label: "No", description: "I'm ready for any workout that matches my experience level" },
          ]
        }
      case "location":
        return {
          title: "Where will you be working out & what equipment do you have access to?",
          icon: Home,
          options: getLocationOptions()
        }
      case "goal":
        return {
          title: "What's your primary goal right now?",
          icon: Target,
          options: getGoalOptions()
        }
      case "experience":
        return {
          title: "What's your exercise experience?",
          icon: User,
          options: getExperienceOptions()
        }
      case "wants_mobility":
        return {
          title: "Do you want a mobility routine added? This will help reduce aches and pains and improve overall performance",
          icon: Heart,
          options: [
            { value: true, label: "Yes", description: "Add a mobility and recovery day" },
            { value: false, label: "No", description: "Focus on main workouts only" },
          ]
        }
      case "flex_score":
        return {
          title: "Rate your flexibility out of 10",
          icon: Heart,
          options: Array.from({ length: 10 }, (_, i) => ({
            value: i + 1,
            label: `${i + 1}`,
            description: i + 1 <= 3 ? "Poor" : i + 1 <= 6 ? "Average" : "Good"
          }))
        }
      default:
        return null
    }
  }

  const currentQuestion = getCurrentQuestion()

  if (showResult && matchedProgram) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center pb-6">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-3xl font-bold text-gray-900 mb-2">
              Your Match: {matchedProgram.name}
            </CardTitle>
            <CardDescription className="text-lg text-gray-600">
              Built around your goal and schedule with no extra fluff.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Location</h4>
                <p className="text-gray-600">{answers.location?.replace(/_/g, ' ')}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Goal</h4>
                <p className="text-gray-600">{answers.goal?.replace(/_/g, ' ')}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Experience</h4>
                <p className="text-gray-600">{answers.experience}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Mobility Add-On</h4>
                <p className="text-gray-600">{getMobilityAddOn() === "none" ? "None" : `${getMobilityAddOn()} mobility`}</p>
              </div>
            </div>

            {/* Equipment Purchase Link for home_willing_to_purchase */}
            {answers.location === "home_willing_to_purchase" && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                <h4 className="font-semibold text-blue-900 mb-2">🛒 Get Your Equipment</h4>
                <p className="text-blue-700 text-sm mb-3">
                  Ready to get the basic equipment for your home workouts? We've curated the essentials you'll need.
                </p>
                <Button
                  onClick={() => window.open('/recommendations', '_blank')}
                  variant="outline"
                  className="w-full border-blue-300 text-blue-700 hover:bg-blue-100"
                >
                  View Recommended Equipment →
                </Button>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button 
                onClick={handleGetStarted}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 text-lg font-semibold"
              >
                Get Started
              </Button>
              <Button 
                onClick={handleRetake}
                variant="outline"
                className="flex-1 py-3 text-lg font-semibold"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Retake Intake
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!currentQuestion) return null

  const IconComponent = currentQuestion.icon

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="pb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <IconComponent className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Question {currentStep + 1} of {questionFlow.length}</p>
                <Progress value={progress} className="w-32 h-2 mt-1" />
              </div>
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 leading-tight">
            {currentQuestion.title}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <RadioGroup 
            value={answers[currentQuestionId]?.toString() || ""} 
            onValueChange={(value) => handleAnswer(currentQuestionId === "has_limitations" || currentQuestionId === "wants_mobility" ? value === "true" : currentQuestionId === "flex_score" ? parseInt(value) : value)}
            className="space-y-3"
          >
            {currentQuestion.options.map((option) => {
              const isSelected = answers[currentQuestionId] === option.value
              return (
                <div key={option.value.toString()} className="relative">
                  <RadioGroupItem 
                    value={option.value.toString()} 
                    id={option.value.toString()}
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor={option.value.toString()}
                    className={`flex items-center justify-between p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 hover:shadow-md ${
                      isSelected 
                        ? 'border-green-500 bg-green-50 shadow-md' 
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}
                  >
                    <div className="flex-1">
                      <div className={`font-semibold ${isSelected ? 'text-green-900' : 'text-gray-900'}`}>
                        {option.label}
                      </div>
                      <div className={`text-sm mt-1 ${isSelected ? 'text-green-700' : 'text-gray-500'}`}>
                        {option.description}
                      </div>
                    </div>
                    <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                      isSelected ? 'border-green-500 bg-green-500' : 'border-gray-300'
                    }`}>
                      {isSelected && <div className="w-2 h-2 bg-white rounded-full" />}
                    </div>
                  </Label>
                </div>
              )
            })}
          </RadioGroup>
          
          <div className="flex justify-between pt-6">
            <Button
              onClick={handlePrevious}
              disabled={currentStep === 0}
              variant="outline"
              className="px-6 py-2"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>
            <Button
              onClick={handleNext}
              disabled={!isAnswered()}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700"
            >
              {currentStep === questionFlow.length - 1 ? "Complete" : "Next"}
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
