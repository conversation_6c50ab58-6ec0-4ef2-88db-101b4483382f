"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { ChevronRight, ChevronLeft, CheckCircle, Target, Dumbbell, User, Heart } from "lucide-react"

interface QuestionnaireProps {
  onComplete: (program: string, answers: Record<string, string | string[]>) => void
}

const questions = [
  {
    id: "serious-injuries",
    question: "Do you have any serious injuries?",
    icon: Heart,
    type: "single",
    options: [
      { value: "yes", label: "Yes", description: "I have serious injuries or limitations" },
      { value: "no", label: "No", description: "I don't have serious injuries" },
    ],
  },
  {
    id: "equipment",
    question: "Where will you be working out & what equipment do you have access to?",
    icon: <PERSON><PERSON><PERSON>,
    type: "single",
    options: [
      { value: "full-gym", label: "Full gym", description: "Complete gym with all equipment" },
      { value: "home-equipment", label: "Home with equipment", description: "Dumbbells, bands, yoga mat" },
      { value: "bodyweight", label: "Home with no equipment", description: "Bodyweight only workouts" },
    ],
  },
  {
    id: "goals",
    question: "What is your main goal?",
    icon: Target,
    type: "single",
    options: [
      { value: "lose-weight", label: "Lose weight", description: "Burn fat and get lean" },
      { value: "build-strength", label: "Build strength", description: "Increase power and muscle" },
      { value: "build-habits", label: "Build healthier habits", description: "Create sustainable routines" },
    ],
  },
  {
    id: "experience",
    question: "What is your experience level?",
    icon: User,
    type: "single",
    options: [
      { value: "beginner", label: "Beginner", description: "New to fitness or returning after a break" },
      { value: "intermediate", label: "Intermediate", description: "Some training experience" },
      { value: "advanced", label: "Advanced", description: "Consistent training experience for 1+ years" },
    ],
  },
  {
    id: "mobility",
    question: "What specific mobility concerns do you have?",
    icon: Heart,
    type: "multiple",
    options: [
      { value: "knee-pain", label: "Knee pain", description: "Knee discomfort or limitations" },
      { value: "back-pain", label: "Back pain", description: "Lower or upper back issues" },
      { value: "hip-pain", label: "Hip pain", description: "Hip mobility or pain concerns" },
      { value: "shoulder-pain", label: "Shoulder pain", description: "Shoulder mobility or pain issues" },
      { value: "other", label: "Other", description: "Other mobility concerns" },
    ],
  },
]

export function Questionnaire({ onComplete }: QuestionnaireProps) {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<Record<string, string | string[]>>({})



  // Get the current question flow based on answers (following flowchart exactly)
  const getQuestionFlow = () => {
    const flow = ["serious-injuries"] // Always start with serious injuries

    // Equipment question - always shown
    flow.push("equipment")

    // Goals question - always shown
    flow.push("goals")

    // Experience question - always shown
    flow.push("experience")

    // Mobility question - only shown if user has serious injuries
    const hasInjuries = answers["serious-injuries"] === "yes"
    if (hasInjuries) {
      flow.push("mobility")
    }

    return flow
  }

  const questionFlow = getQuestionFlow()
  const currentQuestionId = questionFlow[currentQuestion]

  const handleSingleAnswer = (value: string) => {
    setAnswers({ ...answers, [currentQuestionId]: value })
  }

  const handleMultipleAnswer = (value: string, checked: boolean) => {
    const currentAnswers = (answers[currentQuestionId] as string[]) || []

    // Special handling for mobility concerns - "none" is exclusive
    if (currentQuestionId === "mobility") {
      if (value === "none" && checked) {
        // If "none" is selected, clear all other options
        setAnswers({
          ...answers,
          [currentQuestionId]: ["none"]
        })
        return
      } else if (value !== "none" && checked && currentAnswers.includes("none")) {
        // If any other option is selected and "none" was previously selected, remove "none"
        setAnswers({
          ...answers,
          [currentQuestionId]: [value]
        })
        return
      }
    }

    // Normal multiple selection logic
    if (checked) {
      setAnswers({
        ...answers,
        [currentQuestionId]: [...currentAnswers, value]
      })
    } else {
      setAnswers({
        ...answers,
        [currentQuestionId]: currentAnswers.filter(v => v !== value)
      })
    }
  }

  const handleNext = () => {
    if (currentQuestion < questionFlow.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
    } else {
      const program = determineProgram(answers)
      onComplete(program, answers)
    }
  }

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const isQuestionAnswered = () => {
    const currentAnswer = answers[currentQuestionId]
    const questionData = questions.find(q => q.id === currentQuestionId)
    if (questionData?.type === "multiple") {
      return Array.isArray(currentAnswer) && currentAnswer.length > 0
    } else {
      return currentAnswer && currentAnswer !== ""
    }
  }

  const determineProgram = (answers: Record<string, string | string[]>) => {
    const { seriousInjuries, equipment, goals, experience } = answers

    // Build base program name following flowchart format: EXPERIENCE EQUIPMENT GOAL
    let programName = ""

    // Experience level
    let exp = ""
    if (experience === "beginner") {
      exp = "BEGINNER"
    } else if (experience === "intermediate") {
      exp = "INTERMEDIATE"
    } else if (experience === "advanced") {
      exp = "ADVANCED"
    }

    // Equipment mapping
    let equip = ""
    if (equipment === "full-gym") {
      equip = "GYM"
    } else if (equipment === "home-equipment") {
      equip = "HOME"
    } else {
      equip = "BODYWEIGHT"
    }

    // Goal mapping
    let goal = ""
    if (goals === "lose-weight") {
      goal = "FAT LOSS"
    } else if (goals === "build-strength") {
      goal = "STRENGTH"
    } else if (goals === "build-habits") {
      goal = "HABIT BUILDING"
    }

    // Build the base program name
    programName = `${exp} ${equip} ${goal}`

    // Add mobility add-on if user has serious injuries
    const hasInjuries = seriousInjuries === "yes"
    if (hasInjuries) {
      let mobilityAddOn = ""
      if (experience === "beginner") {
        mobilityAddOn = "BEGINNER MOBILITY"
      } else {
        // Both intermediate and advanced use the same mobility add-on
        mobilityAddOn = "INTERMEDIATE MOBILITY"
      }
      programName += ` + ${mobilityAddOn}`
    }

    return programName.trim()
  }

  const progress = ((currentQuestion + 1) / questionFlow.length) * 100

  // Get dynamic goal options based on equipment selection (from flowchart analysis)
  const getGoalOptions = (equipment: string) => {
    if (equipment === "full-gym") {
      // Gym: Only Fat Loss and Build Strength (no Habit Building)
      return [
        { value: "lose-weight", label: "Lose weight", description: "Burn fat and get lean" },
        { value: "build-strength", label: "Build strength", description: "Increase power and muscle" },
      ]
    } else if (equipment === "home-equipment") {
      // Home equipment: Need to check flowchart for exact goals available
      return [
        { value: "lose-weight", label: "Lose weight", description: "Burn fat and get lean" },
        { value: "build-strength", label: "Build strength", description: "Increase power and muscle" },
        { value: "build-habits", label: "Build healthier habits", description: "Create sustainable routines" },
      ]
    } else if (equipment === "bodyweight") {
      // Bodyweight: Need to check flowchart for exact goals available
      return [
        { value: "lose-weight", label: "Lose weight", description: "Burn fat and get lean" },
        { value: "build-habits", label: "Build healthier habits", description: "Create sustainable routines" },
      ]
    }

    // Default fallback
    return [
      { value: "lose-weight", label: "Lose weight", description: "Burn fat and get lean" },
      { value: "build-strength", label: "Build strength", description: "Increase power and muscle" },
      { value: "build-habits", label: "Build healthier habits", description: "Create sustainable routines" },
    ]
  }

  // Get current question data with dynamic options
  const getQuestionData = () => {
    const baseQuestion = questions.find(q => q.id === currentQuestionId)
    if (!baseQuestion) return null

    // For goals question, use dynamic options based on equipment
    if (currentQuestionId === "goals" && answers.equipment) {
      return {
        ...baseQuestion,
        options: getGoalOptions(answers.equipment as string)
      }
    }

    return baseQuestion
  }

  const questionData = getQuestionData()
  const IconComponent = questionData?.icon || Target

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 fitness-gradient"></div>
      <div className="absolute inset-0 bg-black/30"></div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-4xl">
          <Card className="glass-card border-white/30 shadow-2xl bg-white/25 backdrop-blur-xl">
            <div className="absolute inset-0 bg-black/10 rounded-2xl"></div>
            <div className="relative z-10">
              <CardHeader className="text-center pb-8">
                <div className="flex justify-center mb-4">
                  <div className="p-4 bg-white/30 rounded-2xl backdrop-blur-sm border border-white/20">
                    <IconComponent className="h-8 w-8 text-white drop-shadow-lg" />
                  </div>
                </div>
                <CardTitle className="text-3xl font-bold text-white mb-2 drop-shadow-lg">Fitness Assessment</CardTitle>
                <CardDescription className="text-white/95 text-lg drop-shadow-md font-medium">
                  Help us create the perfect workout program for you
                </CardDescription>

                {/* Enhanced Progress Bar */}
                <div className="mt-8 space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-white font-semibold drop-shadow-sm">Progress</span>
                    <span className="text-white font-semibold drop-shadow-sm">
                      {currentQuestion + 1} of {questionFlow.length}
                    </span>
                  </div>
                  <div className="relative bg-white/20 rounded-full h-3 backdrop-blur-sm border border-white/30">
                    <div
                      className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full transition-all duration-500 ease-out shadow-lg"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-8">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-white mb-8 drop-shadow-lg">
                    {questionData?.question}
                  </h3>

                  {questionData?.type === "single" ? (
                    <RadioGroup
                      value={answers[currentQuestionId] as string || ""}
                      onValueChange={handleSingleAnswer}
                      className="grid grid-cols-1 md:grid-cols-2 gap-4"
                    >
                      {questionData.options.map((option) => {
                        const isSelected = (answers[currentQuestionId] as string) === option.value

                        return (
                          <div key={option.value} className="relative">
                            <RadioGroupItem value={option.value} id={option.value} className="peer sr-only" />
                            <Label
                              htmlFor={option.value}
                              className={`flex flex-col p-6 border-3 rounded-2xl cursor-pointer transition-all duration-300 group backdrop-blur-sm shadow-lg hover:shadow-xl transform ${
                                isSelected
                                  ? 'bg-gradient-to-br from-green-400/30 to-blue-400/30 border-green-300 shadow-green-300/20 shadow-2xl scale-105'
                                  : 'bg-white/10 border-white/40 hover:bg-white/20 hover:border-white/60 hover:scale-102'
                              }`}
                            >
                              <div className="flex items-center justify-between mb-3">
                                <span className={`text-lg font-bold drop-shadow-md transition-colors ${
                                  isSelected ? 'text-green-100' : 'text-white'
                                }`}>{option.label}</span>
                                <CheckCircle className={`h-6 w-6 transition-all duration-300 drop-shadow-sm ${
                                  isSelected
                                    ? 'text-green-300 scale-110'
                                    : 'text-white/50 group-hover:text-white/80'
                                }`} />
                              </div>
                              <span className={`text-sm text-left font-medium drop-shadow-sm transition-colors ${
                                isSelected ? 'text-white/95' : 'text-white/80'
                              }`}>
                                {option.description}
                              </span>
                            </Label>
                          </div>
                        )
                      })}
                    </RadioGroup>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {questionData?.options.map((option) => {
                        const currentAnswers = (answers[currentQuestionId] as string[]) || []
                        const isChecked = currentAnswers.includes(option.value)

                        return (
                          <div key={option.value} className="relative">
                            <Label
                              className={`flex flex-col p-6 border-3 rounded-2xl cursor-pointer transition-all duration-300 group backdrop-blur-sm shadow-lg hover:shadow-xl transform ${
                                isChecked
                                  ? 'bg-gradient-to-br from-green-400/30 to-blue-400/30 border-green-300 shadow-green-300/20 shadow-2xl scale-105 text-green-100'
                                  : 'bg-white/10 border-white/40 hover:bg-white/20 hover:border-white/60 hover:scale-102'
                              }`}
                            >
                              <div className="flex items-center justify-between mb-3">
                                <span className={`text-lg font-bold drop-shadow-md transition-colors ${
                                  isChecked ? 'text-green-100' : 'text-white'
                                }`}>{option.label}</span>
                                <Checkbox
                                  checked={isChecked}
                                  onCheckedChange={(checked) => handleMultipleAnswer(option.value, checked as boolean)}
                                  className={`h-6 w-6 border-2 transition-all duration-300 ${
                                    isChecked
                                      ? 'border-green-300 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-400 scale-110'
                                      : 'border-white/50 hover:border-white/80'
                                  }`}
                                />
                              </div>
                              <span className={`text-sm text-left font-medium drop-shadow-sm transition-colors ${
                                isChecked ? 'text-white/95' : 'text-white/80'
                              }`}>
                                {option.description}
                              </span>
                            </Label>
                          </div>
                        )
                      })}
                    </div>
                  )}
                </div>

                <div className="flex justify-between items-center pt-4">
                  {/* Previous Button */}
                  {currentQuestion > 0 ? (
                    <Button
                      onClick={handlePrevious}
                      variant="outline"
                      className="bg-white/20 text-white border-white/40 hover:bg-white/30 hover:border-white/60 font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg"
                    >
                      <ChevronLeft className="mr-2 h-5 w-5" />
                      Previous
                    </Button>
                  ) : (
                    <div></div> // Empty div to maintain spacing
                  )}

                  {/* Next Button */}
                  <Button
                    onClick={handleNext}
                    disabled={!isQuestionAnswered()}
                    className="bg-white text-purple-600 hover:bg-white/95 font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg text-lg"
                  >
                    {currentQuestion < questionFlow.length - 1 ? (
                      <>
                        Next Question
                        <ChevronRight className="ml-2 h-5 w-5" />
                      </>
                    ) : (
                      "Complete Assessment"
                    )}
                  </Button>
                </div>
              </CardContent>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
