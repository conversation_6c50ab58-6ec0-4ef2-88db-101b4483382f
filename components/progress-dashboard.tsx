"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from "recharts"
import { Calendar, Camera, Trophy, TrendingUp, Target, Zap, FlameIcon as Fire, Award, Clock } from "lucide-react"
import { ProgressDashboardProps } from "@/lib/types"
import { useIsMobile } from "@/hooks/use-mobile"

// Enhanced mock data
const mockWeightData = [
  { date: "Jan 1", weight: 180, goal: 175 },
  { date: "Jan 8", weight: 179, goal: 175 },
  { date: "Jan 15", weight: 178.5, goal: 175 },
  { date: "Jan 22", weight: 177, goal: 175 },
  { date: "Jan 29", weight: 176.5, goal: 175 },
  { date: "Feb 5", weight: 175, goal: 175 },
  { date: "Feb 12", weight: 174.5, goal: 175 },
]

const mockPersonalRecords = [
  { exercise: "Bench Press", weight: "225 lbs", date: "Feb 10", improvement: "+15 lbs" },
  { exercise: "Squat", weight: "315 lbs", date: "Feb 8", improvement: "+25 lbs" },
  { exercise: "Deadlift", weight: "405 lbs", date: "Feb 5", improvement: "+35 lbs" },
]

const mockWorkoutData = [
  { week: "Week 1", workouts: 3, target: 4 },
  { week: "Week 2", workouts: 4, target: 4 },
  { week: "Week 3", workouts: 3, target: 4 },
  { week: "Week 4", workouts: 5, target: 4 },
  { week: "Week 5", workouts: 4, target: 4 },
  { week: "Week 6", workouts: 4, target: 4 },
]

export function ProgressDashboard({ user }: ProgressDashboardProps) {
  const isMobile = useIsMobile()
  const weeklyWorkouts = 4
  const totalWorkouts = 28
  const currentStreak = 12
  const weightLoss = 5.5

  return (
    <div className="space-y-8">
      {/* Enhanced Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700">This Week</CardTitle>
            <div className="p-2 bg-blue-500 rounded-lg">
              <Calendar className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-900">{weeklyWorkouts}</div>
            <p className="text-xs text-blue-600 flex items-center mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              workouts completed
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700">Total Sessions</CardTitle>
            <div className="p-2 bg-purple-500 rounded-lg">
              <Zap className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-900">{totalWorkouts}</div>
            <p className="text-xs text-purple-600 flex items-center mt-1">
              <Fire className="h-3 w-3 mr-1" />
              since joining
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700">Weight Progress</CardTitle>
            <div className="p-2 bg-green-500 rounded-lg">
              <TrendingUp className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-900">-{weightLoss} lbs</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <Target className="h-3 w-3 mr-1" />
              last 6 weeks
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700">Current Streak</CardTitle>
            <div className="p-2 bg-orange-500 rounded-lg">
              <Fire className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-orange-900">{currentStreak}</div>
            <p className="text-xs text-orange-600 flex items-center mt-1">
              <Clock className="h-3 w-3 mr-1" />
              days strong
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Enhanced Weight Progress Chart */}
        <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center text-xl">
              <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
              Weight Progress
            </CardTitle>
            <CardDescription>Track your transformation journey</CardDescription>
          </CardHeader>
          <CardContent>
            <div className={`chart-container rounded-xl ${
              isMobile ? 'h-[280px] p-2' : 'h-[300px] p-4'
            }`}>
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={mockWeightData}
                  margin={
                    isMobile
                      ? { top: 10, right: 10, left: 10, bottom: 10 }
                      : { top: 20, right: 30, left: 20, bottom: 20 }
                  }
                >
                  <defs>
                    <linearGradient id="weightGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3} />
                      <stop offset="95%" stopColor="#3B82F6" stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke="#E5E7EB"
                    opacity={isMobile ? 0.5 : 1}
                  />
                  <XAxis
                    dataKey="date"
                    stroke="#6B7280"
                    fontSize={isMobile ? 12 : 14}
                    tick={{ fontSize: isMobile ? 12 : 14 }}
                    tickMargin={isMobile ? 8 : 5}
                  />
                  <YAxis
                    domain={["dataMin - 2", "dataMax + 2"]}
                    stroke="#6B7280"
                    fontSize={isMobile ? 12 : 14}
                    tick={{ fontSize: isMobile ? 12 : 14 }}
                    tickMargin={isMobile ? 8 : 5}
                    width={isMobile ? 45 : 60}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: "rgba(255, 255, 255, 0.95)",
                      border: "none",
                      borderRadius: "12px",
                      boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                      fontSize: isMobile ? '14px' : '16px',
                      padding: isMobile ? '8px 12px' : '12px 16px',
                      minWidth: isMobile ? '120px' : 'auto'
                    }}
                    labelFormatter={(value) => `Date: ${value}`}
                    formatter={(value, name) => [`${value} lbs`, name === "weight" ? "Current Weight" : "Goal Weight"]}
                  />
                  <Area
                    type="monotone"
                    dataKey="weight"
                    stroke="#3B82F6"
                    strokeWidth={isMobile ? 2 : 3}
                    fill="url(#weightGradient)"
                  />
                  <Line
                    type="monotone"
                    dataKey="goal"
                    stroke="#10B981"
                    strokeWidth={isMobile ? 1.5 : 2}
                    strokeDasharray="5 5"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Workout Frequency Chart */}
        <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center text-xl">
              <Zap className="h-5 w-5 mr-2 text-purple-600" />
              Weekly Activity
            </CardTitle>
            <CardDescription>Consistency is key to success</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] chart-container rounded-xl p-4">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={mockWorkoutData}>
                  <defs>
                    <linearGradient id="workoutGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.3} />
                      <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                  <XAxis dataKey="week" stroke="#6B7280" />
                  <YAxis stroke="#6B7280" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: "rgba(255, 255, 255, 0.95)",
                      border: "none",
                      borderRadius: "12px",
                      boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                    }}
                  />
                  <Area
                    type="monotone"
                    dataKey="workouts"
                    stroke="#8B5CF6"
                    strokeWidth={3}
                    fill="url(#workoutGradient)"
                  />
                  <Line type="monotone" dataKey="target" stroke="#F59E0B" strokeWidth={2} strokeDasharray="5 5" />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Enhanced Personal Records */}
        <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center text-xl">
              <Trophy className="h-5 w-5 mr-2 text-yellow-600" />
              Personal Records
            </CardTitle>
            <CardDescription>Your latest achievements and milestones</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockPersonalRecords.map((record, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl border border-yellow-200 hover:shadow-md transition-all duration-300"
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-yellow-500 rounded-lg">
                      <Award className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">{record.exercise}</p>
                      <p className="text-sm text-gray-600">{record.date}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 font-semibold">
                      {record.weight}
                    </Badge>
                    <p className="text-xs text-green-600 font-medium mt-1">{record.improvement}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Progress Photos */}
        <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center text-xl">
              <Camera className="h-5 w-5 mr-2 text-blue-600" />
              Progress Gallery
            </CardTitle>
            <CardDescription>Visual journey of your transformation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <div className="p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl mb-6 inline-block">
                <Camera className="h-16 w-16 text-blue-400 mx-auto" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Start Your Visual Journey</h3>
              <p className="text-gray-600 mb-6">Upload your first progress photo to track your transformation</p>
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105">
                <Camera className="h-4 w-4 mr-2" />
                Upload First Photo
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
