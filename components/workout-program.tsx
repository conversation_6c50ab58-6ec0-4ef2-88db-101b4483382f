"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Clock, Repeat, Weight, Play, CheckCircle, Target, Zap, Video } from "lucide-react"
import { WorkoutSession } from "@/components/workout-session"
import { ExerciseVideoModal } from "@/components/exercise-video-modal"
import { useState } from "react"

interface WorkoutProgramProps {
  program: string
}

const workoutPrograms = {
  "Beginner Fat Loss": {
    description: "A balanced program focusing on fat loss through cardio and strength training",
    schedule: "3 days per week",
    duration: "45 minutes",
    difficulty: "Beginner",
    workouts: [
      {
        day: "Day 1: Full Body Foundation",
        description: "Build your foundation with compound movements",
        exercises: [
          { name: "Bodyweight Squats", sets: "3", reps: "12-15", rest: "60s", muscle: "Legs" },
          { name: "Push-ups", sets: "3", reps: "8-12", rest: "60s", muscle: "Chest" },
          { name: "Bent-over Row<PERSON>", sets: "3", reps: "10-12", rest: "60s", muscle: "Back" },
          { name: "Plank", sets: "3", reps: "30-45s", rest: "60s", muscle: "Core" },
          { name: "Walking/Treadmill", sets: "1", reps: "15 min", rest: "-", muscle: "Cardio" },
        ],
      },
      {
        day: "Day 2: Cardio Blast",
        description: "High-intensity cardio for maximum fat burn",
        exercises: [
          { name: "Jumping Jacks", sets: "3", reps: "30s", rest: "30s", muscle: "Full Body" },
          { name: "Mountain Climbers", sets: "3", reps: "30s", rest: "30s", muscle: "Core" },
          { name: "Burpees", sets: "3", reps: "5-8", rest: "60s", muscle: "Full Body" },
          { name: "High Knees", sets: "3", reps: "30s", rest: "30s", muscle: "Legs" },
          { name: "Steady State Cardio", sets: "1", reps: "20 min", rest: "-", muscle: "Cardio" },
        ],
      },
    ],
  },
  "Beginner Muscle Building": {
    description: "Foundation program for building muscle mass and strength",
    schedule: "4 days per week",
    duration: "60 minutes",
    difficulty: "Beginner",
    workouts: [
      {
        day: "Day 1: Upper Body Power",
        description: "Focus on building upper body strength",
        exercises: [
          { name: "Bench Press", sets: "3", reps: "8-10", rest: "90s", muscle: "Chest" },
          { name: "Bent-over Rows", sets: "3", reps: "8-10", rest: "90s", muscle: "Back" },
          { name: "Overhead Press", sets: "3", reps: "8-10", rest: "90s", muscle: "Shoulders" },
          { name: "Lat Pulldowns", sets: "3", reps: "10-12", rest: "60s", muscle: "Back" },
          { name: "Bicep Curls", sets: "3", reps: "10-12", rest: "60s", muscle: "Arms" },
        ],
      },
    ],
  },
}

const muscleColors = {
  Legs: "bg-blue-100 text-blue-800",
  Chest: "bg-red-100 text-red-800",
  Back: "bg-green-100 text-green-800",
  Core: "bg-yellow-100 text-yellow-800",
  Cardio: "bg-purple-100 text-purple-800",
  "Full Body": "bg-indigo-100 text-indigo-800",
  Shoulders: "bg-orange-100 text-orange-800",
  Arms: "bg-pink-100 text-pink-800",
}

export function WorkoutProgram({ program }: WorkoutProgramProps) {
  const [activeWorkout, setActiveWorkout] = useState<any>(null)
  const [isWorkoutActive, setIsWorkoutActive] = useState(false)
  const [selectedExercise, setSelectedExercise] = useState<any>(null)
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false)

  const currentProgram =
    workoutPrograms[program as keyof typeof workoutPrograms] || workoutPrograms["Beginner Fat Loss"]

  const handleWatchDemo = (exercise: any) => {
    setSelectedExercise(exercise)
    setIsVideoModalOpen(true)
  }

  return (
    <div className="space-y-8">
      {/* Program Overview */}
      <Card className="bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 text-white border-0 shadow-2xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-3xl font-bold mb-2">{program}</CardTitle>
              <CardDescription className="text-white/90 text-lg">{currentProgram.description}</CardDescription>
            </div>
            <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm">
              <Target className="h-8 w-8 text-white" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="flex items-center space-x-3 bg-white/10 rounded-xl p-4 backdrop-blur-sm">
              <Clock className="h-5 w-5 text-white/80" />
              <div>
                <p className="text-sm text-white/80">Duration</p>
                <p className="font-semibold">{currentProgram.duration}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 bg-white/10 rounded-xl p-4 backdrop-blur-sm">
              <Repeat className="h-5 w-5 text-white/80" />
              <div>
                <p className="text-sm text-white/80">Frequency</p>
                <p className="font-semibold">{currentProgram.schedule}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 bg-white/10 rounded-xl p-4 backdrop-blur-sm">
              <Weight className="h-5 w-5 text-white/80" />
              <div>
                <p className="text-sm text-white/80">Level</p>
                <p className="font-semibold">{currentProgram.difficulty}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 bg-white/10 rounded-xl p-4 backdrop-blur-sm">
              <Zap className="h-5 w-5 text-white/80" />
              <div>
                <p className="text-sm text-white/80">Intensity</p>
                <p className="font-semibold">Progressive</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Workout Days */}
      <div className="space-y-6">
        {currentProgram.workouts.map((workout, index) => (
          <Card
            key={index}
            className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300"
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-2">{workout.day}</CardTitle>
                  <CardDescription className="text-gray-600 text-base">{workout.description}</CardDescription>
                </div>
                <Button
                  size="lg"
                  onClick={() => {
                    setActiveWorkout(workout)
                    setIsWorkoutActive(true)
                  }}
                  className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105"
                >
                  <Play className="h-5 w-5 mr-2" />
                  Start Workout
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {workout.exercises.map((exercise, exerciseIndex) => (
                  <div key={exerciseIndex} className="group">
                    <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl border border-gray-200 hover:shadow-md transition-all duration-300">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-blue-500 rounded-lg group-hover:bg-blue-600 transition-colors">
                          <CheckCircle className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 text-lg">{exercise.name}</h4>
                          <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                            <span className="flex items-center">
                              <Weight className="h-3 w-3 mr-1" />
                              {exercise.sets} sets
                            </span>
                            <span className="flex items-center">
                              <Repeat className="h-3 w-3 mr-1" />
                              {exercise.reps} reps
                            </span>
                            {exercise.rest !== "-" && (
                              <span className="flex items-center">
                                <Clock className="h-3 w-3 mr-1" />
                                {exercise.rest} rest
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleWatchDemo(exercise)}
                          className="opacity-0 group-hover:opacity-100 transition-all duration-300 bg-white/80 hover:bg-white border-blue-200 hover:border-blue-300"
                        >
                          <Video className="h-4 w-4 mr-1 text-blue-600" />
                          <span className="text-blue-600 font-medium">Watch Demo</span>
                        </Button>
                        <Badge
                          className={`${muscleColors[exercise.muscle as keyof typeof muscleColors] || "bg-gray-100 text-gray-800"} font-medium px-3 py-1`}
                        >
                          {exercise.muscle}
                        </Badge>
                        <Badge variant="outline" className="font-semibold">
                          {exercise.sets} × {exercise.reps}
                        </Badge>
                      </div>
                    </div>
                    {exerciseIndex < workout.exercises.length - 1 && (
                      <Separator className="my-2 bg-gradient-to-r from-transparent via-gray-300 to-transparent" />
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Workout Session Modal/Overlay */}
      {isWorkoutActive && activeWorkout && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
          <WorkoutSession
            workout={activeWorkout}
            onComplete={() => {
              setIsWorkoutActive(false)
              setActiveWorkout(null)
              alert("🎉 Workout completed! Great job!")
            }}
            onExit={() => {
              if (confirm("Are you sure you want to end this workout?")) {
                setIsWorkoutActive(false)
                setActiveWorkout(null)
              }
            }}
          />
        </div>
      )}

      {/* Exercise Video Modal */}
      {selectedExercise && (
        <ExerciseVideoModal
          exercise={selectedExercise}
          isOpen={isVideoModalOpen}
          onClose={() => {
            setIsVideoModalOpen(false)
            setSelectedExercise(null)
          }}
        />
      )}
    </div>
  )
}
