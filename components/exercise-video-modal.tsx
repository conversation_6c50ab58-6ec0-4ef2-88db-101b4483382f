"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Play, Pause, RotateCcw, Volume2, VolumeX, Maximize, Info, Target, AlertTriangle } from "lucide-react"

interface ExerciseVideoModalProps {
  exercise: {
    name: string
    sets: string
    reps: string
    rest: string
    muscle: string
  }
  isOpen: boolean
  onClose: () => void
}

// Mock video data - in a real app, these would be actual video URLs
const exerciseVideos = {
  "Bodyweight Squats": {
    videoUrl: "/placeholder.svg?height=400&width=600&text=Bodyweight+Squats+Demo",
    duration: "0:45",
    difficulty: "Beginner",
    equipment: "None",
    instructions: [
      "Stand with feet shoulder-width apart, toes slightly turned out",
      "Keep your chest up and core engaged throughout the movement",
      "Lower your body by pushing your hips back and bending your knees",
      "Descend until your thighs are parallel to the floor",
      "Push through your heels to return to the starting position",
      "Keep your knees aligned with your toes throughout the movement",
    ],
    tips: [
      "Keep your weight on your heels, not your toes",
      "Don't let your knees cave inward",
      "Maintain a neutral spine throughout the movement",
      "Control the descent - don't drop down quickly",
    ],
    commonMistakes: ["Knees caving inward", "Leaning too far forward", "Not going deep enough", "Rising up on toes"],
  },
  "Push-ups": {
    videoUrl: "/placeholder.svg?height=400&width=600&text=Push-ups+Demo",
    duration: "0:30",
    difficulty: "Beginner",
    equipment: "None",
    instructions: [
      "Start in a plank position with hands slightly wider than shoulders",
      "Keep your body in a straight line from head to heels",
      "Lower your chest toward the floor by bending your elbows",
      "Push back up to the starting position",
      "Keep your core tight throughout the movement",
    ],
    tips: [
      "Keep your body rigid like a plank",
      "Don't let your hips sag or pike up",
      "Control both the lowering and pushing phases",
      "Breathe in on the way down, out on the way up",
    ],
    commonMistakes: [
      "Sagging hips",
      "Flaring elbows too wide",
      "Not going full range of motion",
      "Head position too high or low",
    ],
  },
  "Bent-over Rows": {
    videoUrl: "/placeholder.svg?height=400&width=600&text=Bent-over+Rows+Demo",
    duration: "0:40",
    difficulty: "Intermediate",
    equipment: "Dumbbells or Barbell",
    instructions: [
      "Stand with feet hip-width apart, holding weights",
      "Hinge at the hips and lean forward, keeping back straight",
      "Let the weights hang with arms extended",
      "Pull the weights up to your lower chest/upper abdomen",
      "Squeeze your shoulder blades together at the top",
      "Lower the weights with control",
    ],
    tips: [
      "Keep your core engaged to protect your lower back",
      "Focus on pulling with your back muscles, not your arms",
      "Keep your neck in a neutral position",
      "Don't use momentum - control the weight",
    ],
    commonMistakes: [
      "Rounding the back",
      "Using too much arm instead of back",
      "Not maintaining hip hinge position",
      "Jerky, uncontrolled movements",
    ],
  },
  Plank: {
    videoUrl: "/placeholder.svg?height=400&width=600&text=Plank+Demo",
    duration: "0:25",
    difficulty: "Beginner",
    equipment: "None",
    instructions: [
      "Start in a push-up position on your forearms",
      "Keep your body in a straight line from head to heels",
      "Engage your core and glutes",
      "Hold the position while breathing normally",
      "Keep your neck neutral, looking at the floor",
    ],
    tips: [
      "Squeeze your glutes to help maintain position",
      "Don't hold your breath - breathe normally",
      "Focus on quality over duration",
      "Keep your shoulders directly over your elbows",
    ],
    commonMistakes: [
      "Hips sagging toward the floor",
      "Hips too high in the air",
      "Holding breath",
      "Looking up instead of down",
    ],
  },
  "Bench Press": {
    videoUrl: "/placeholder.svg?height=400&width=600&text=Bench+Press+Demo",
    duration: "0:50",
    difficulty: "Intermediate",
    equipment: "Barbell, Bench",
    instructions: [
      "Lie on the bench with eyes under the barbell",
      "Grip the bar with hands slightly wider than shoulders",
      "Plant your feet firmly on the ground",
      "Unrack the bar and position it over your chest",
      "Lower the bar to your chest with control",
      "Press the bar back up to the starting position",
    ],
    tips: [
      "Keep your shoulder blades pulled back and down",
      "Maintain a slight arch in your lower back",
      "Keep your feet planted throughout the movement",
      "Control the weight on both the way down and up",
    ],
    commonMistakes: [
      "Bouncing the bar off the chest",
      "Flaring elbows too wide",
      "Lifting feet off the ground",
      "Not using full range of motion",
    ],
  },
  "Overhead Press": {
    videoUrl: "/placeholder.svg?height=400&width=600&text=Overhead+Press+Demo",
    duration: "0:45",
    difficulty: "Intermediate",
    equipment: "Dumbbells or Barbell",
    instructions: [
      "Stand with feet shoulder-width apart",
      "Hold weights at shoulder level with palms facing forward",
      "Engage your core and keep your back straight",
      "Press the weights straight up overhead",
      "Lower the weights back to shoulder level with control",
    ],
    tips: [
      "Keep your core tight to protect your lower back",
      "Don't arch your back excessively",
      "Press the weights in a straight line",
      "Keep your wrists straight and strong",
    ],
    commonMistakes: [
      "Excessive back arch",
      "Pressing the weights forward instead of up",
      "Not engaging the core",
      "Using legs to help press the weight",
    ],
  },
}

export function ExerciseVideoModal({ exercise, isOpen, onClose }: ExerciseVideoModalProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [activeTab, setActiveTab] = useState<"instructions" | "tips" | "mistakes">("instructions")

  const videoData = exerciseVideos[exercise.name as keyof typeof exerciseVideos] || {
    videoUrl: "/placeholder.svg?height=400&width=600&text=Exercise+Demo",
    duration: "0:30",
    difficulty: "Beginner",
    equipment: "None",
    instructions: ["Demo video coming soon"],
    tips: ["Check back later for detailed tips"],
    commonMistakes: ["Video content being prepared"],
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Beginner":
        return "bg-green-100 text-green-800"
      case "Intermediate":
        return "bg-yellow-100 text-yellow-800"
      case "Advanced":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold flex items-center">
            <Play className="h-6 w-6 mr-2 text-blue-600" />
            {exercise.name} - Exercise Demo
          </DialogTitle>
          <DialogDescription>
            Learn proper form and technique with our detailed exercise demonstration
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Video Section */}
          <div className="lg:col-span-2 space-y-4">
            {/* Video Player */}
            <Card className="bg-black rounded-xl overflow-hidden">
              <div className="relative aspect-video bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
                <img
                  src={videoData.videoUrl || "/placeholder.svg"}
                  alt={`${exercise.name} demonstration`}
                  className="w-full h-full object-cover"
                />

                {/* Video Controls Overlay */}
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
                  <div className="flex items-center space-x-4">
                    <Button
                      size="lg"
                      onClick={() => setIsPlaying(!isPlaying)}
                      className="bg-white/20 hover:bg-white/30 text-white border-white/40 backdrop-blur-sm"
                    >
                      {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => setIsMuted(!isMuted)}
                      className="bg-white/20 hover:bg-white/30 text-white border-white/40 backdrop-blur-sm"
                    >
                      {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                    </Button>
                    <Button
                      size="sm"
                      className="bg-white/20 hover:bg-white/30 text-white border-white/40 backdrop-blur-sm"
                    >
                      <Maximize className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Video Info Overlay */}
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="bg-black/60 backdrop-blur-sm rounded-lg p-3 text-white">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{exercise.name}</span>
                      <span className="text-sm">{videoData.duration}</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-1 mt-2">
                      <div className="bg-blue-500 h-1 rounded-full w-1/3"></div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Exercise Details */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Target className="h-5 w-5 mr-2 text-blue-600" />
                    Exercise Details
                  </CardTitle>
                  <div className="flex items-center space-x-2">
                    <Badge className={getDifficultyColor(videoData.difficulty)}>{videoData.difficulty}</Badge>
                    <Badge variant="outline">{exercise.muscle}</Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="font-semibold text-blue-900">{exercise.sets}</div>
                    <div className="text-sm text-blue-600">Sets</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="font-semibold text-green-900">{exercise.reps}</div>
                    <div className="text-sm text-green-600">Reps</div>
                  </div>
                  <div className="text-center p-3 bg-orange-50 rounded-lg">
                    <div className="font-semibold text-orange-900">{exercise.rest}</div>
                    <div className="text-sm text-orange-600">Rest</div>
                  </div>
                  <div className="text-center p-3 bg-purple-50 rounded-lg">
                    <div className="font-semibold text-purple-900">{videoData.equipment}</div>
                    <div className="text-sm text-purple-600">Equipment</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Instructions Section */}
          <div className="space-y-4">
            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab("instructions")}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  activeTab === "instructions"
                    ? "bg-white text-blue-600 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <Info className="h-4 w-4 inline mr-1" />
                Steps
              </button>
              <button
                onClick={() => setActiveTab("tips")}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  activeTab === "tips" ? "bg-white text-green-600 shadow-sm" : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <Target className="h-4 w-4 inline mr-1" />
                Tips
              </button>
              <button
                onClick={() => setActiveTab("mistakes")}
                className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                  activeTab === "mistakes" ? "bg-white text-red-600 shadow-sm" : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <AlertTriangle className="h-4 w-4 inline mr-1" />
                Avoid
              </button>
            </div>

            {/* Tab Content */}
            <Card className="h-[400px] overflow-y-auto">
              <CardHeader>
                <CardTitle className="text-lg">
                  {activeTab === "instructions" && "Step-by-Step Instructions"}
                  {activeTab === "tips" && "Pro Tips"}
                  {activeTab === "mistakes" && "Common Mistakes"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {activeTab === "instructions" &&
                    videoData.instructions.map((instruction, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        <p className="text-gray-700 leading-relaxed">{instruction}</p>
                      </div>
                    ))}

                  {activeTab === "tips" &&
                    videoData.tips.map((tip, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center">
                          <Target className="h-3 w-3" />
                        </div>
                        <p className="text-gray-700 leading-relaxed">{tip}</p>
                      </div>
                    ))}

                  {activeTab === "mistakes" &&
                    videoData.commonMistakes.map((mistake, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center">
                          <AlertTriangle className="h-3 w-3" />
                        </div>
                        <p className="text-gray-700 leading-relaxed">{mistake}</p>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="space-y-2">
              <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                <Play className="h-4 w-4 mr-2" />
                Start This Exercise
              </Button>
              <Button variant="outline" className="w-full bg-transparent">
                <RotateCcw className="h-4 w-4 mr-2" />
                Replay Video
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
