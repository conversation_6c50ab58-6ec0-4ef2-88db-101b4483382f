"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Apple, ChevronDown, ChevronUp, Scale, Utensils, BookOpen, Calculator } from "lucide-react"

export function NutritionGuide() {
  // State for collapsible sections
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    intro: false,
    protein: false,
    carbs: false,
    fats: false,
    summary: false
  })

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  // Educational content sections
  const macroSections = [
    {
      id: 'protein',
      title: 'Protein',
      icon: '🥩',
      content: (
        <div className="space-y-4 text-base leading-relaxed">
          <p className="text-gray-700">
            💡 Aim for 25–40g per meal depending on your needs
          </p>
          <p className="text-gray-600 mt-3 font-medium">
            Protein is made up of amino acids—essential building blocks for:
          </p>
          <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
            <li>Strength, muscle repair and growth</li>
            <li>Immune function</li>
            <li>Hormone production</li>
            <li>Enzymes and neurotransmitters</li>
            <li>Satiety (keeping you fuller, longer)</li>
          </ul>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800 font-medium">
              ⚠️ Without enough protein, your body can break down lean tissue (like muscle) for energy—especially during weight loss or aging.
            </p>
          </div>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-3">Examples of High-Quality Protein:</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-gray-700">
              <div>• Chicken breast, turkey, steak, fish, egg whites</div>
              <div>• Greek yogurt, cottage cheese, protein powder</div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'carbs',
      title: 'Carbohydrates',
      icon: '🍞',
      content: (
        <div className="space-y-4 text-base leading-relaxed">
          <p className="text-gray-700">
            Carbs are broken down into glucose, your brain and muscles' #1 energy source.
          </p>
          <p className="text-gray-700">You need carbs for:</p>
          <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
            <li>Fueling workouts</li>
            <li>Supporting thyroid and hormone health</li>
            <li>Promoting sleep and serotonin</li>
            <li>Aiding recovery and muscle glycogen replenishment</li>
          </ul>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-blue-800 font-medium mb-3">
              💡 Not all carbs are created equal. Choose:
            </p>
            <div className="space-y-2 text-blue-700">
              <div>• <strong>Whole grains:</strong> oats, quinoa, brown rice, whole grain sourdough bread</div>
              <div>• <strong>Starchy vegetables:</strong> sweet potatoes, squash, root veggies</div>
              <div>• <strong>Fruit:</strong> berries, apples, bananas</div>
              <div>• <strong>Legumes</strong> (if tolerated)</div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'fats',
      title: 'Fats',
      icon: '🥑',
      content: (
        <div className="space-y-4 text-base leading-relaxed">
          <p className="text-gray-700">
            Fats are crucial for:
          </p>
          <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
            <li>Hormone production (especially estrogen, testosterone, and cortisol)</li>
            <li>Absorbing fat-soluble vitamins (A, D, E, K)</li>
            <li>Brain function and mental clarity</li>
            <li>Satiety and blood sugar balance</li>
          </ul>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-semibold text-yellow-900 mb-3">Healthy fats to focus on:</h4>
            <div className="space-y-2 text-yellow-800">
              <div>• Avocado, olive oil, grass-fed butter</div>
              <div>• Nuts/seeds (in moderation)</div>
              <div>• Fatty fish (omega-3s)</div>
              <div>• Egg yolks</div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'summary',
      title: 'Why Macros Matter for Body Composition',
      icon: '⚖️',
      content: (
        <div className="space-y-4 text-base leading-relaxed">
          <p className="text-gray-700">
            Balanced macros are the key to:
          </p>
          <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
            <li>Losing fat while preserving muscle</li>
            <li>Building lean, toned muscle</li>
            <li>Avoiding cravings and energy crashes</li>
            <li>Supporting metabolism and hormonal balance</li>
          </ul>
          <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 text-center">
            <p className="text-lg font-semibold text-gray-900 mb-2">
              🔑 Key Takeaway
            </p>
            <p className="text-gray-700">
              It's not just about calories—it's about <strong>what makes up those calories</strong>.
            </p>
          </div>
        </div>
      )
    }
  ]

  // Portion Control educational content
  const portionSections = [
    {
      id: 'portion-basics',
      title: 'Understanding Portion Sizes',
      icon: '📏',
      content: (
        <div className="space-y-4 text-base leading-relaxed">
          <p className="text-gray-700">
            Portion control is about eating the right amount of food for your body's needs. Here are simple visual guides:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="font-semibold text-red-800 mb-3 flex items-center">
                🥩 Protein Portions
              </h4>
              <div className="space-y-2 text-red-700">
                <div>• <strong>Palm size:</strong> 3-4 oz cooked meat/fish</div>
                
                <div>• <strong>Fist size:</strong> 1 cup Greek yogurt or cottage cheese</div>
              </div>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-3 flex items-center">
                🍞 Carbohydrate Portions
              </h4>
              <div className="space-y-2 text-blue-700">
                <div>• <strong>Cupped hand:</strong> 1/2 cup starchy veg/rice/pasta</div>
                <div>• <strong>Fist size:</strong> 1 medium fruit</div>
                <div>• <strong>Slice of bread:</strong> 1 serving of grain</div>
              </div>
            </div>
          </div>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-semibold text-yellow-800 mb-3 flex items-center">
              🥑 Fat Portions
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-yellow-700">
              <div>• <strong>Thumb tip:</strong> 1 tsp oil/nut butter</div>
              <div>• <strong>Small handful:</strong> 1 oz nuts</div>
              <div>• <strong>Golf ball:</strong> 1/4 avocado</div>
              
              
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'measuring-tips',
      title: 'Plating Your Food',
      icon: '🍽️ ',
      content: (
        <div className="space-y-6 text-base leading-relaxed">
          <p className="text-gray-700">
            Use the plate method to create balanced, portion-controlled meals:
          </p>

          {/* Horizontal Layout: Image + Text Guidelines */}
          <div className="flex flex-col lg:flex-row lg:items-start lg:gap-8 space-y-6 lg:space-y-0">
            {/* Plate Diagram */}
            <div className="flex justify-center lg:justify-start lg:flex-shrink-0">
              <img
                src="/plate-method-diagram.png"
                alt="Plate Method Diagram showing proper food proportions: vegetables, protein, and carbs sections"
                className="w-full max-w-xs sm:max-w-sm lg:max-w-xs h-auto"
              />
            </div>

            {/* Text Guidelines */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 lg:flex-1">
              <h4 className="font-semibold text-green-800 mb-3">🍽️ The Plate Method</h4>
              <div className="space-y-2 text-green-700">
                <div>• <strong>1/2 plate:</strong> Non-starchy vegetables (leafy greens, broccoli, peppers)</div>
                <div>• <strong>1/4 plate:</strong> Lean protein (fish, chicken, tofu, beans)</div>
                <div>• <strong>1/4 plate:</strong> Complex carbohydrates (sweet potato, quinoa, brown rice)</div>
                <div>• <strong>Add:</strong> 1-2 servings healthy fats (olive oil, avocado, nuts)</div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'smart-tips',
      title: 'Smart Portion Tips',
      icon: '💡',
      content: (
        <div className="space-y-4 text-base leading-relaxed">
          <p className="text-gray-700">
            Simple strategies to control portions without obsessing over measurements:
          </p>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <h4 className="font-semibold text-purple-800 mb-3">💡 Smart Portion Tips</h4>
            <ul className="list-disc list-inside space-y-2 text-purple-700 ml-4">
              <li>Fill up on protein and vegetables first</li>
              <li>Use smaller plates and bowls</li>
              <li>Eat slowly and mindfully</li>
              <li>Stop eating when 80% full</li>
              <li>Pre-portion snacks into containers</li>
                        </ul>
          </div>
        </div>
      )
    }
  ]

  // Suggested Foods educational content
  const foodSections = [
    {
      id: 'beneficial-foods',
      title: 'Beneficial Foods List',
      icon: '✅',
      content: (
        <div className="space-y-4 text-base leading-relaxed">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <p className="text-green-800 font-medium">
              The foods you eat 80-90% of the time should support your energy, digestion, metabolism, and long-term health.
              These nutrient-dense options will help reduce inflammation, improve body composition, and keep you feeling your best.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Meat Section */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="font-semibold text-red-800 mb-3">🥩 Meat</h4>
              <p className="text-red-700 text-sm mb-3">Stay away from highly processed meats and meats high in unhealthy fat.</p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-red-700 text-sm">
                <div>• Poultry</div>
                <div>• Turkey</div>
                <div>• Chicken breast</div>
                <div>• Steak</div>
                <div>• Bison</div>
                <div>• Bison jerky</div>
                <div>• Bison ribeye</div>
                <div>• Bison sirloin</div>
                <div>• Veal</div>
                <div>• Bacon</div>
                <div>• Lamb chops</div>
                <div>• Rabbit</div>
                <div>• Ground beef</div>
                <div>• Goat</div>
                <div>• Grass-fed beef</div>
                <div>• Elk</div>
                <div>• Chicken thigh</div>
                <div>• Chicken leg</div>
                <div>• Chicken wings</div>
                <div>• Lamb rack</div>
                <div>• Beef jerky</div>
                <div>• Eggs (duck, chicken, goose)</div>
                <div>• Wild boar</div>
                <div>• Venison steaks</div>
                <div>• Quail</div>
                <div>• Lean veal</div>
                <div>• Buffalo</div>
                <div>• Chuck steak</div>
                <div>• New York steak</div>
              </div>
            </div>

            {/* Fish & Seafood Section */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-3">🐟 Fish & Seafood</h4>
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-blue-700 mb-2">Fish:</h5>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-blue-700 text-sm">
                    <div>• Bass</div>
                    <div>• Salmon</div>
                    <div>• Halibut</div>
                    <div>• Mackerel</div>
                    <div>• Sardines</div>
                    <div>• Tuna</div>
                    <div>• Red snapper</div>
                    <div>• Shark</div>
                    <div>• Sunfish</div>
                    <div>• Swordfish</div>
                    <div>• Tilapia</div>
                    <div>• Trout</div>
                    <div>• Walleye</div>
                  </div>
                </div>
                <div>
                  <h5 className="font-medium text-blue-700 mb-2">Seafood:</h5>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-blue-700 text-sm">
                    <div>• Crab</div>
                    <div>• Crawfish</div>
                    <div>• Crayfish</div>
                    <div>• Shrimp</div>
                    <div>• Clams</div>
                    <div>• Lobster</div>
                    <div>• Scallops</div>
                    <div>• Oysters</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Vegetables */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-semibold text-green-800 mb-3">🥬 Vegetables</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-green-700 text-sm">
                <div>• Asparagus</div>
                <div>• Avocado</div>
                <div>• Artichoke hearts</div>
                <div>• Brussels sprouts</div>
                <div>• Carrots</div>
                <div>• Spinach</div>
                <div>• Celery</div>
                <div>• Broccoli</div>
                <div>• Zucchini</div>
                <div>• Cabbage</div>
                <div>• Peppers (all kinds)</div>
                <div>• Cauliflower</div>
                <div>• Parsley</div>
                <div>• Eggplant</div>
                <div>• Green onions</div>
              </div>
            </div>

            {/* Starchy Vegetables */}
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <h4 className="font-semibold text-orange-800 mb-3">🍠 Starchy Vegetables & Carb Substitutes</h4>
              <p className="text-orange-700 text-sm mb-3">
                Consume in moderation - especially when trying to reduce inflammation, control insulin, and/or lose weight.
              </p>
              <div className="space-y-1 text-orange-700 text-sm">
                <div>• Butternut squash</div>
                <div>• Brown Rice</div>
                <div>• Acorn squash</div>
                <div>• Sweet potato</div>
                <div>• Yam</div>
                <div>• Beets (good for liver detox)</div>
                <div>• Quinoa</div>
              </div>
            </div>

            {/* Oils/Fats */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-800 mb-3">🫒 Oils/Fats</h4>
              <div className="space-y-1 text-yellow-700 text-sm">
                <div>• Coconut oil</div>
                <div>• Avocado oil</div>
                <div>• Olive oil</div>
                <div>• Grass-fed butter</div>
                <div>• Macadamia oil</div>
              </div>
            </div>

            {/* Nuts */}
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <h4 className="font-semibold text-amber-800 mb-3">🥜 Nuts</h4>
              <p className="text-amber-700 text-xs mb-2">Be careful with portions - high in fat and calories</p>
              <div className="space-y-1 text-amber-700 text-sm">
                <div>• Almonds</div>
                <div>• Cashews</div>
                <div>• Pumpkin seeds</div>
                <div>• Sunflower seeds</div>
                <div>• Hazelnuts</div>
                <div>• Macadamia nuts</div>
                <div>• Pecans</div>
                <div>• Walnuts</div>
                <div>• Pine nuts</div>
              </div>
              <p className="text-amber-600 text-xs mt-2">Note: Peanuts are legumes, not nuts!</p>
            </div>

            {/* Fruits */}
            <div className="bg-pink-50 border border-pink-200 rounded-lg p-4">
              <h4 className="font-semibold text-pink-800 mb-3">🍎 Fruits</h4>
              <p className="text-pink-700 text-xs mb-2">Keep servings at 1-2/day - contains fructose (sugar)</p>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Berries */}
                <div>
                  <h5 className="font-medium text-pink-800 mb-2 text-sm">🫐 Berries</h5>
                  <div className="space-y-1 text-pink-700 text-sm">
                    <div>• Blackberries</div>
                    <div>• Blueberries</div>
                    <div>• Raspberries</div>
                    <div>• Strawberries</div>
                  </div>
                </div>

                {/* Citrus */}
                <div>
                  <h5 className="font-medium text-pink-800 mb-2 text-sm">🍊 Citrus</h5>
                  <div className="space-y-1 text-pink-700 text-sm">
                    <div>• Lemon</div>
                    <div>• Lime</div>
                    <div>• Oranges</div>
                    <div>• Tangerine</div>
                  </div>
                </div>

                {/* Stone Fruits */}
                <div>
                  <h5 className="font-medium text-pink-800 mb-2 text-sm">🍑 Stone Fruits</h5>
                  <div className="space-y-1 text-pink-700 text-sm">
                    <div>• Peaches</div>
                    <div>• Plums</div>
                  </div>
                </div>

                {/* Tropical */}
                <div>
                  <h5 className="font-medium text-pink-800 mb-2 text-sm">🥭 Tropical</h5>
                  <div className="space-y-1 text-pink-700 text-sm">
                    <div>• Mango</div>
                    <div>• Papaya</div>
                    <div>• Pineapple guava</div>
                    <div>• Lychee</div>
                  </div>
                </div>

                {/* Melons */}
                <div>
                  <h5 className="font-medium text-pink-800 mb-2 text-sm">🍉 Melons</h5>
                  <div className="space-y-1 text-pink-700 text-sm">
                    <div>• Cantaloupe</div>
                    <div>• Watermelon</div>
                  </div>
                </div>

                {/* Other */}
                <div>
                  <h5 className="font-medium text-pink-800 mb-2 text-sm">🍎 Other</h5>
                  <div className="space-y-1 text-pink-700 text-sm">
                    <div>• Apple</div>
                    <div>• Avocado</div>
                    <div>• Bananas</div>
                    <div>• Figs</div>
                    <div>• Grapes</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'foods-to-limit',
      title: 'Foods to Limit',
      icon: '⚠️',
      content: (
        <div className="space-y-4 text-base leading-relaxed">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800 font-medium">
              This is not to say you can NEVER eat these foods, but these are the foods you want to consume in limited moderation 10-20% of the time.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Non-Negotiable White Foods */}
            <div className="bg-gray-50 border border-gray-300 rounded-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-3">🚫 White Foods'</h4>
              <p className="text-gray-700 text-sm mb-3">These spike insulin, contribute to inflammation, and negatively impact cholesterol and triglycerides.</p>
              <div className="space-y-1 text-gray-700 text-sm">
                <div>• White sugar</div>
                <div>• White flour</div>
                <div>• White salt (iodized)</div>
                <div>• White rice</div>
                <div>• White pasta</div>
                <div>• White bread</div>
              </div>
            </div>

            {/* Seed Oils */}
            <div className="bg-orange-50 border border-orange-300 rounded-lg p-4">
              <h4 className="font-semibold text-orange-800 mb-3">🚫 Seed Oils</h4>
              <p className="text-orange-700 text-sm mb-3">High in omega-6 fatty acids, contributing to systemic and liver inflammation.</p>
              <div className="space-y-1 text-orange-700 text-sm">
                <div>• Canola oil</div>
                <div>• Soybean oil</div>
                <div>• Sunflower oil</div>
                <div>• Corn oil</div>
                <div>• Safflower oil</div>
                <div>• Cottonseed oil</div>
              </div>
            </div>

            {/* Processed Dairy */}
            <div className="bg-blue-50 border border-blue-300 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-3">🥛 Highly Processed Dairy</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-blue-700 text-sm">
                <div>• Processed cheese</div>
                <div>• Non-fat dairy creamer</div>
                <div>• Skim milk</div>
                <div>• 2% milk</div>
                <div>• Whole milk (sometimes)</div>
                <div>• Dairy spreads</div>
                <div>• Cream cheese</div>
                <div>• Powdered milk</div>
                <div>• Pudding</div>
                <div>• Frozen yogurt</div>
                <div>• Ice milk</div>
                <div>• Low fat milk</div>
                <div>• Ice cream</div>
              </div>
            </div>

            {/* Soft Drinks & Juices */}
            <div className="bg-purple-50 border border-purple-300 rounded-lg p-4">
              <h4 className="font-semibold text-purple-800 mb-3">🥤 Soft Drinks & Fruit Juices</h4>
              <p className="text-purple-700 text-sm mb-3">Packed with sugar and high-fructose corn syrup.</p>
              <div className="space-y-2">
                <div>
                  <h5 className="font-medium text-purple-700 text-sm">Soft Drinks:</h5>
                  <div className="text-purple-700 text-sm">• Coke • Sprite • Pepsi • Mountain Dew</div>
                </div>
                <div>
                  <h5 className="font-medium text-purple-700 text-sm">Fruit Juices:</h5>
                  <div className="text-purple-700 text-sm">• Apple juice • Orange juice • Grape juice • Mango juice</div>
                </div>
              </div>
            </div>

            {/* Processed Grains */}
            <div className="bg-yellow-50 border border-yellow-300 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-800 mb-3">🌾 Processed Grains</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-yellow-700 text-sm">
                <div>• Cereals</div>
                <div>• Bread</div>
                <div>• Corn syrup</div>
                <div>• High-fructose corn syrup</div>
                <div>• Pancakes</div>
                <div>• English muffins</div>
                <div>• Toast</div>
                <div>• Sandwiches</div>
                <div>• Triscuits</div>
                <div>• Wheat Thins</div>
                <div>• Hash browns</div>
                <div>• Beer</div>
                <div>• Pasta</div>
                <div>• Fettuccini</div>
                <div>• Lasagne</div>
                <div>• Crackers</div>
                <div>• Corn</div>
              </div>
            </div>

            {/* Legumes */}
            <div className="bg-green-50 border border-green-300 rounded-lg p-4">
              <h4 className="font-semibold text-green-800 mb-3">🫘 Legumes</h4>
              <p className="text-green-700 text-sm mb-3">Can cause inflammation if you're not diet typed to them culturally.</p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-green-700 text-sm">
                <div>• Black beans</div>
                <div>• White beans</div>
                <div>• Broad beans</div>
                <div>• Fava beans</div>
                <div>• Garbanzo beans</div>
                <div>• Horse beans</div>
                <div>• Kidney beans</div>
                <div>• Lima beans</div>
                <div>• Mung beans</div>
                <div>• Adzuki beans</div>
                <div>• Navy beans</div>
                <div>• Pinto beans</div>
                <div>• Red beans</div>
                <div>• Green beans</div>
                <div>• String beans</div>
                <div>• Black-eyed peas</div>
                <div>• Chickpeas</div>
                <div>• Snow-peas</div>
                <div>• Sugar snap peas</div>
                <div>• Peanuts</div>
                <div>• Peanut butter</div>
                <div>• Miso</div>
                <div>• Lentils</div>
                <div>• Lupins</div>
                <div>• Mesquite</div>
                <div>• Soybeans</div>
                <div>• All soybean products</div>
                <div>• Tofu</div>
              </div>
            </div>

            {/* Artificial Sweeteners */}
            <div className="bg-pink-50 border border-pink-300 rounded-lg p-4">
              <h4 className="font-semibold text-pink-800 mb-3">🧪 Artificial Sweeteners</h4>
              <p className="text-pink-700 text-sm mb-3">Use honey, maple syrup, or Stevia instead.</p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-pink-700 text-sm">
                <div>• Acesulfame potassium</div>
                <div>• Aspartame</div>
                <div>• Aspartame-acesulfame salt</div>
                <div>• Cyclamate</div>
                <div>• Erythritol</div>
                <div>• Glycerol</div>
                <div>• Glycyrrhizin</div>
                <div>• Maltitol</div>
                <div>• Mannitol</div>
                <div>• Neotame</div>
                <div>• Polydextrose</div>
                <div>• Saccharin</div>
                <div>• Sorbitol</div>
                <div>• Steviol glycoside</div>
                <div>• Sucralose</div>
                <div>• Isomalt</div>
                <div>• Tagatose</div>
                <div>• Lactitol</div>
                <div>• Xylitol</div>
              </div>
            </div>

            {/* Snacks & Energy Drinks */}
            <div className="bg-red-50 border border-red-300 rounded-lg p-4">
              <h4 className="font-semibold text-red-800 mb-3">🍿 Snacks & Energy Drinks</h4>
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-red-700 text-sm mb-2">Snacks:</h5>
                  <div className="space-y-1 text-red-700 text-sm">
                    <div>• Pretzels</div>
                    <div>• Chips</div>
                    <div>• Cookies</div>
                    <div>• Pastries</div>
                    <div>• Candy</div>
                  </div>
                </div>
                <div>
                  <h5 className="font-medium text-red-700 text-sm mb-2">Energy Drinks:</h5>
                  <div className="space-y-1 text-red-700 text-sm">
                    <div>• Red Bull</div>
                    <div>• Monster</div>
                    <div>• And similar products</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <Card className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 text-white border-0 shadow-2xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-3xl font-bold mb-2 flex items-center">
                <Apple className="h-8 w-8 mr-3" />
                Nutrition Education Hub
              </CardTitle>
              <CardDescription className="text-white/90 text-lg">
                Learn the fundamentals of nutrition for better health and fitness
              </CardDescription>
            </div>
            <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm">
              <span className="text-2xl">📚</span>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Educational Tabs */}
      <Tabs defaultValue="macros" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 bg-white/80 backdrop-blur-xl p-2 rounded-2xl shadow-lg border border-white/20">
          <TabsTrigger
            value="macros"
            className="flex items-center space-x-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-600 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-xl transition-all duration-300"
          >
            <BookOpen className="h-4 w-4" />
            <span>Understanding Macros</span>
          </TabsTrigger>
          <TabsTrigger
            value="portions"
            className="flex items-center space-x-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-600 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-xl transition-all duration-300"
          >
            <Scale className="h-4 w-4" />
            <span>Portion Control</span>
          </TabsTrigger>
          <TabsTrigger
            value="foods"
            className="flex items-center space-x-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-600 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-xl transition-all duration-300"
          >
            <Utensils className="h-4 w-4" />
            <span>Suggested Foods</span>
          </TabsTrigger>
        </TabsList>

        {/* Understanding Macros Tab */}
        <TabsContent value="macros">
          <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-200">
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
                📖 Understanding Macros
              </CardTitle>
              <CardDescription className="text-gray-600 text-lg">
                Master the building blocks of nutrition for optimal body composition
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                {/* Static What Are Macronutrients Section */}
                <div className="border border-gray-200 rounded-xl p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-3">
                    <span className="text-2xl">🎯</span>
                    What Are Macronutrients?
                  </h3>
                  <div className="space-y-4 text-base leading-relaxed">
                    <p className="text-gray-700">
                      Macronutrients (or "macros") are the nutrients your body needs in large amounts to function properly:
                    </p>
                    <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
                      <li>Protein (4 calories per gram)</li>
                      <li>Carbs (4 calories per gram)</li>
                      <li>Fat (9 calories per gram)</li>
                    </ul>
                    <p className="text-gray-700">
                      These three macros make up your calorie intake and play different roles in your metabolism, energy, recovery, and body composition.
                    </p>
                  </div>
                </div>

                {/* Collapsible sections for other macro content */}
                {macroSections.map((section) => (
                  <div key={section.id} className="border border-gray-200 rounded-xl overflow-hidden">
                    <Button
                      onClick={() => toggleSection(section.id)}
                      variant="ghost"
                      className="w-full p-6 h-auto justify-between text-left hover:bg-gray-50 rounded-none"
                    >
                      <div className="flex items-center gap-4">
                        <span className="text-2xl">{section.icon}</span>
                        <span className="text-lg font-semibold text-gray-900">{section.title}</span>
                      </div>
                      {expandedSections[section.id] ? (
                        <ChevronUp className="h-6 w-6 text-gray-500" />
                      ) : (
                        <ChevronDown className="h-6 w-6 text-gray-500" />
                      )}
                    </Button>

                    {expandedSections[section.id] && (
                      <div className="px-6 pb-6 bg-gray-50/50">
                        {section.content}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Portion Control Tab */}
        <TabsContent value="portions">
          <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
            <CardHeader className="bg-gradient-to-r from-orange-50 to-red-50 border-b border-gray-200">
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
                ⚖️ Portion Control
              </CardTitle>
              <CardDescription className="text-gray-600 text-lg">
                Learn practical portion sizes and visual guides for balanced eating
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                {portionSections.map((section) => (
                  <div key={section.id} className="border border-gray-200 rounded-xl overflow-hidden">
                    <Button
                      onClick={() => toggleSection(section.id)}
                      variant="ghost"
                      className="w-full p-6 h-auto justify-between text-left hover:bg-gray-50 rounded-none"
                    >
                      <div className="flex items-center gap-4">
                        <span className="text-2xl">{section.icon}</span>
                        <span className="text-lg font-semibold text-gray-900">{section.title}</span>
                      </div>
                      {expandedSections[section.id] ? (
                        <ChevronUp className="h-6 w-6 text-gray-500" />
                      ) : (
                        <ChevronDown className="h-6 w-6 text-gray-500" />
                      )}
                    </Button>

                    {expandedSections[section.id] && (
                      <div className="px-6 pb-6 bg-gray-50/50">
                        {section.content}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Suggested Foods Tab */}
        <TabsContent value="foods">
          <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
            <CardHeader className="bg-gradient-to-r from-green-50 to-teal-50 border-b border-gray-200">
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
                🍽️ Suggested Foods
              </CardTitle>
              <CardDescription className="text-gray-600 text-lg">
                Discover quality food choices organized by macronutrient type
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                {foodSections.map((section) => (
                  <div key={section.id} className="border border-gray-200 rounded-xl overflow-hidden">
                    <Button
                      onClick={() => toggleSection(section.id)}
                      variant="ghost"
                      className="w-full p-6 h-auto justify-between text-left hover:bg-gray-50 rounded-none"
                    >
                      <div className="flex items-center gap-4">
                        <span className="text-2xl">{section.icon}</span>
                        <span className="text-lg font-semibold text-gray-900">{section.title}</span>
                      </div>
                      {expandedSections[section.id] ? (
                        <ChevronUp className="h-6 w-6 text-gray-500" />
                      ) : (
                        <ChevronDown className="h-6 w-6 text-gray-500" />
                      )}
                    </Button>

                    {expandedSections[section.id] && (
                      <div className="px-6 pb-6 bg-gray-50/50">
                        {section.content}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}