"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { AlertTriangle, Trash2 } from "lucide-react"
import { WeightService, WeightEntry } from "@/lib/services/weight-service"

interface DeleteWeightModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  userId: string
  targetDate: string
  targetWeight: number
  weightEntries: WeightEntry[]
}

export function DeleteWeightModal({
  isOpen,
  onClose,
  onSuccess,
  userId,
  targetDate,
  targetWeight,
  weightEntries
}: DeleteWeightModalProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const [error, setError] = useState<string>("")

  // Find the entry to delete
  const entryToDelete = weightEntries.find(
    entry => entry.recorded_date === targetDate && entry.weight_value === targetWeight
  )

  // Check if this is the only weight entry
  const isOnlyEntry = weightEntries.length === 1

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const handleDelete = async () => {
    if (!entryToDelete?.id) {
      setError("Weight entry not found")
      return
    }

    if (isOnlyEntry) {
      setError("Cannot delete your only weight entry. Add another entry first.")
      return
    }

    setIsDeleting(true)
    setError("")

    try {
      const { error: deleteError } = await WeightService.deleteWeightEntry(entryToDelete.id)

      if (deleteError) {
        throw deleteError
      }

      onSuccess()
      onClose()
    } catch (error: any) {
      console.error('Error deleting weight entry:', error)
      setError(error.message || 'Failed to delete weight entry')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleClose = () => {
    if (!isDeleting) {
      setError("")
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Delete Weight Entry
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. The weight entry will be permanently removed from your records.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Entry Details */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-semibold text-red-900 mb-2">Entry to Delete:</h4>
            <div className="space-y-1 text-sm text-red-800">
              <p><strong>Weight:</strong> {targetWeight} lbs</p>
              <p><strong>Date:</strong> {formatDate(targetDate)}</p>
              {entryToDelete?.notes && (
                <p><strong>Notes:</strong> {entryToDelete.notes}</p>
              )}
            </div>
          </div>

          {/* Warning Messages */}
          {isOnlyEntry ? (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-yellow-900">Cannot Delete Only Entry</h4>
                  <p className="text-sm text-yellow-800 mt-1">
                    This is your only weight entry. You must have at least one weight entry to track your progress. 
                    Add another weight entry before deleting this one.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-red-900">Permanent Deletion</h4>
                  <p className="text-sm text-red-800 mt-1">
                    This weight entry will be permanently deleted and cannot be recovered. 
                    Your progress charts and statistics will be updated automatically.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isDeleting}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting || isOnlyEntry}
              className="flex-1"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {isDeleting ? "Deleting..." : "Delete Entry"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
