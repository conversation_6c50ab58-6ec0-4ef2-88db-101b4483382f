"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Weight } from "lucide-react"
import { format, isValid } from "date-fns"
import { WeightService, WeightEntry } from "@/lib/services/weight-service"

interface EditWeightModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  userId: string
  initialDate: string
  initialWeight: number
  weightEntries: WeightEntry[]
}

export function EditWeightModal({
  isOpen,
  onClose,
  onSuccess,
  userId,
  initialDate,
  initialWeight,
  weightEntries
}: EditWeightModalProps) {
  const [weight, setWeight] = useState(initialWeight.toString())
  const [date, setDate] = useState<Date>(() => {
    const parsedDate = new Date(initialDate)
    return isValid(parsedDate) ? parsedDate : new Date()
  })
  const [notes, setNotes] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<{weight?: string, date?: string, notes?: string}>({})

  // Find the existing entry to get current notes
  useEffect(() => {
    if (isOpen) {
      const existingEntry = weightEntries.find(
        entry => entry.recorded_date === initialDate && entry.weight_value === initialWeight
      )
      if (existingEntry) {
        setNotes(existingEntry.notes || "")
      }
      setWeight(initialWeight.toString())
      const parsedDate = new Date(initialDate)
      setDate(isValid(parsedDate) ? parsedDate : new Date())
      setErrors({})
    }
  }, [isOpen, initialDate, initialWeight, weightEntries])

  const validateForm = () => {
    const newErrors: {weight?: string, date?: string, notes?: string} = {}

    // Validate weight
    const weightValue = parseFloat(weight)
    if (!weight || isNaN(weightValue)) {
      newErrors.weight = "Weight is required and must be a valid number"
    } else if (weightValue < 50 || weightValue > 500) {
      newErrors.weight = "Weight must be between 50 and 500 lbs"
    }

    // Validate date
    if (!date) {
      newErrors.date = "Date is required"
    } else if (date > new Date()) {
      newErrors.date = "Date cannot be in the future"
    } else {
      // Check for duplicate dates (excluding current entry)
      const dateString = date.toISOString().split('T')[0]
      const duplicateEntry = weightEntries.find(
        entry => entry.recorded_date === dateString && 
        !(entry.recorded_date === initialDate && entry.weight_value === initialWeight)
      )
      if (duplicateEntry) {
        newErrors.date = "A weight entry already exists for this date"
      }
    }

    // Validate notes
    if (notes && notes.length > 500) {
      newErrors.notes = "Notes must be 500 characters or less"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      // Find the entry to update
      const entryToUpdate = weightEntries.find(
        entry => entry.recorded_date === initialDate && entry.weight_value === initialWeight
      )

      if (!entryToUpdate?.id) {
        throw new Error("Weight entry not found")
      }

      const { error } = await WeightService.updateWeightEntry(entryToUpdate.id, {
        weight_value: parseFloat(weight),
        recorded_date: date.toISOString().split('T')[0],
        notes: notes.trim() || undefined,
        updated_at: new Date().toISOString()
      })

      if (error) {
        throw error
      }

      onSuccess()
      onClose()
    } catch (error: any) {
      console.error('Error updating weight entry:', error)
      setErrors({ weight: error.message || 'Failed to update weight entry' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    if (!isLoading) {
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Weight className="h-5 w-5 text-blue-600" />
            Edit Weight Entry
          </DialogTitle>
          <DialogDescription>
            Update your weight entry details. All fields are validated to ensure data accuracy.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Weight Input */}
          <div>
            <Label htmlFor="weight">Weight (lbs)</Label>
            <Input
              id="weight"
              type="number"
              step="0.1"
              min="50"
              max="500"
              value={weight}
              onChange={(e) => setWeight(e.target.value)}
              placeholder="Enter weight"
              className={errors.weight ? "border-red-500" : ""}
              disabled={isLoading}
            />
            {errors.weight && (
              <p className="text-sm text-red-500 mt-1">{errors.weight}</p>
            )}
          </div>

          {/* Date Picker */}
          <div>
            <Label>Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={`w-full justify-start text-left font-normal ${
                    errors.date ? "border-red-500" : ""
                  }`}
                  disabled={isLoading}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date && isValid(date) ? format(date, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(newDate) => newDate && setDate(newDate)}
                  disabled={(date) => date > new Date()}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            {errors.date && (
              <p className="text-sm text-red-500 mt-1">{errors.date}</p>
            )}
          </div>

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Notes (optional)</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add any notes about this weight entry..."
              maxLength={500}
              rows={3}
              className={errors.notes ? "border-red-500" : ""}
              disabled={isLoading}
            />
            <div className="flex justify-between items-center mt-1">
              {errors.notes && (
                <p className="text-sm text-red-500">{errors.notes}</p>
              )}
              <p className="text-sm text-gray-500 ml-auto">
                {notes.length}/500 characters
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
