"use client"

import { useState, useEffect, useCallback, useMemo, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ExerciseVideoModal } from "@/components/exercise-video-modal"
import { OptimizedTimer } from "@/components/ui/optimized-timer"
import { Square, CheckCircle, Clock, Repeat, SkipForward, RotateCcw, Trophy, Target, Timer, Video } from "lucide-react"
import { WorkoutSessionProps } from "@/lib/types"

export function WorkoutSession({ workout, onComplete, onExit }: WorkoutSessionProps) {
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0)
  const [currentSet, setCurrentSet] = useState(1)
  const [isResting, setIsResting] = useState(false)
  const [restDuration, setRestDuration] = useState(0)
  const [workoutStarted, setWorkoutStarted] = useState(true)
  const [completedSets, setCompletedSets] = useState<Record<number, number[]>>({})
  const [exerciseNotes, setExerciseNotes] = useState<Record<number, string>>({})
  const [currentWeight, setCurrentWeight] = useState("")
  const [currentReps, setCurrentReps] = useState("")
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false)

  const currentExercise = workout.exercises[currentExerciseIndex]
  const totalExercises = workout.exercises.length
  const progress = useMemo(() =>
    ((currentExerciseIndex + currentSet / Number.parseInt(currentExercise.sets)) / totalExercises) * 100,
    [currentExerciseIndex, currentSet, currentExercise.sets, totalExercises]
  )

  // Handle rest completion
  const handleRestComplete = useCallback(() => {
    setIsResting(false)
  }, [])

  const handleCompleteSet = useCallback(() => {
    const exerciseIndex = currentExerciseIndex
    const setData = {
      weight: currentWeight,
      reps: currentReps,
      completed: true,
    }

    setCompletedSets((prev) => ({
      ...prev,
      [exerciseIndex]: [...(prev[exerciseIndex] || []), setData],
    }))

    if (currentSet < Number.parseInt(currentExercise.sets)) {
      setCurrentSet((prev) => prev + 1)
      // Start rest timer if not the last set
      if (currentExercise.rest !== "-") {
        const restSeconds = Number.parseInt(currentExercise.rest.replace("s", ""))
        setRestDuration(restSeconds)
        setIsResting(true)
      }
    } else {
      // Move to next exercise
      if (currentExerciseIndex < totalExercises - 1) {
        setCurrentExerciseIndex((prev) => prev + 1)
        setCurrentSet(1)
        setCurrentWeight("")
        setCurrentReps("")
      } else {
        // Workout complete
        onComplete()
      }
    }
  }, [currentExerciseIndex, currentWeight, currentReps, currentExercise, currentSet, totalExercises, onComplete])

  const handleSkipRest = useCallback(() => {
    setIsResting(false)
  }, [])

  const handlePreviousExercise = useCallback(() => {
    if (currentExerciseIndex > 0) {
      setCurrentExerciseIndex((prev) => prev - 1)
      setCurrentSet(1)
      setCurrentWeight("")
      setCurrentReps("")
    }
  }, [currentExerciseIndex])

  const handleNextExercise = useCallback(() => {
    if (currentExerciseIndex < totalExercises - 1) {
      setCurrentExerciseIndex((prev) => prev + 1)
      setCurrentSet(1)
      setCurrentWeight("")
      setCurrentReps("")
    }
  }, [currentExerciseIndex, totalExercises])

  const handleWatchDemo = () => {
    setIsVideoModalOpen(true)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white border-0 shadow-2xl">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-2xl font-bold mb-2">{workout.day}</CardTitle>
                <CardDescription className="text-white/90">
                  Exercise {currentExerciseIndex + 1} of {totalExercises} • Set {currentSet} of {currentExercise.sets}
                </CardDescription>
              </div>
              <div className="text-right">
                <OptimizedTimer
                  isActive={workoutStarted}
                  className="text-3xl font-bold text-white"
                  label=""
                />
                <p className="text-white/80 text-sm">Workout Time</p>
              </div>
            </div>
            <Progress value={progress} className="mt-4 h-2 bg-white/20" />
          </CardHeader>
        </Card>

        {/* Rest Timer */}
        {isResting && (
          <Card className="bg-gradient-to-r from-orange-500 to-red-500 text-white border-0 shadow-xl animate-pulse">
            <CardContent className="text-center py-8">
              <Timer className="h-12 w-12 mx-auto mb-4" />
              <h3 className="text-2xl font-bold mb-2">Rest Time</h3>
              <OptimizedTimer
                isActive={isResting}
                initialValue={restDuration}
                countDown={true}
                onComplete={handleRestComplete}
                className="text-4xl font-bold mb-4 text-white"
              />
              <Button
                onClick={handleSkipRest}
                variant="outline"
                className="bg-white/20 border-white/40 text-white hover:bg-white/30"
              >
                Skip Rest
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Current Exercise */}
        <Card className="bg-white/90 backdrop-blur-sm shadow-2xl">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-3xl font-bold text-gray-900 mb-2">{currentExercise.name}</CardTitle>
                <div className="flex items-center space-x-4 text-gray-600">
                  <Badge className="bg-blue-100 text-blue-800">{currentExercise.muscle}</Badge>
                  <span className="flex items-center">
                    <Repeat className="h-4 w-4 mr-1" />
                    {currentExercise.sets} sets
                  </span>
                  <span className="flex items-center">
                    <Target className="h-4 w-4 mr-1" />
                    {currentExercise.reps} reps
                  </span>
                  {currentExercise.rest !== "-" && (
                    <span className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {currentExercise.rest} rest
                    </span>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  onClick={handleWatchDemo}
                  className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 hover:from-blue-100 hover:to-purple-100"
                >
                  <Video className="h-4 w-4 mr-2 text-blue-600" />
                  <span className="text-blue-600 font-medium">Watch Demo</span>
                </Button>
                <div className="text-right">
                  <div className="text-2xl font-bold text-blue-600">
                    Set {currentSet}/{currentExercise.sets}
                  </div>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Set Logging */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="weight" className="text-lg font-semibold">
                  Weight (lbs)
                </Label>
                <Input
                  id="weight"
                  type="number"
                  value={currentWeight}
                  onChange={(e) => setCurrentWeight(e.target.value)}
                  placeholder="Enter weight"
                  className="text-lg p-4"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="reps" className="text-lg font-semibold">
                  Reps Completed
                </Label>
                <Input
                  id="reps"
                  type="number"
                  value={currentReps}
                  onChange={(e) => setCurrentReps(e.target.value)}
                  placeholder="Enter reps"
                  className="text-lg p-4"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4 justify-center">
              <Button
                onClick={handleCompleteSet}
                disabled={!currentWeight || !currentReps || isResting}
                className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105"
              >
                <CheckCircle className="h-5 w-5 mr-2" />
                Complete Set
              </Button>

              <Button
                onClick={handlePreviousExercise}
                disabled={currentExerciseIndex === 0}
                variant="outline"
                className="py-3 px-6 rounded-xl bg-transparent"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <Button
                onClick={handleNextExercise}
                disabled={currentExerciseIndex === totalExercises - 1}
                variant="outline"
                className="py-3 px-6 rounded-xl bg-transparent"
              >
                <SkipForward className="h-4 w-4 mr-2" />
                Next
              </Button>

              <Button onClick={onExit} variant="destructive" className="py-3 px-6 rounded-xl">
                <Square className="h-4 w-4 mr-2" />
                End Workout
              </Button>
            </div>

            {/* Completed Sets Display */}
            {completedSets[currentExerciseIndex] && completedSets[currentExerciseIndex].length > 0 && (
              <div className="mt-6">
                <h4 className="font-semibold text-gray-900 mb-3">Completed Sets:</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {completedSets[currentExerciseIndex].map((set, index) => (
                    <div
                      key={index}
                      className="p-3 bg-green-50 border border-green-200 rounded-lg flex items-center justify-between"
                    >
                      <span className="font-medium">Set {index + 1}</span>
                      <span className="text-green-700 font-semibold">
                        {set.weight}lbs × {set.reps}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Exercise List */}
        <Card className="bg-white/80 backdrop-blur-sm shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Trophy className="h-5 w-5 mr-2 text-yellow-600" />
              Workout Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {workout.exercises.map((exercise, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                    index === currentExerciseIndex
                      ? "border-blue-500 bg-blue-50"
                      : completedSets[index] && completedSets[index].length === Number.parseInt(exercise.sets)
                        ? "border-green-500 bg-green-50"
                        : "border-gray-200 bg-gray-50"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold text-gray-900">{exercise.name}</h4>
                      <p className="text-sm text-gray-600">
                        {exercise.sets} sets × {exercise.reps} reps
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {index === currentExerciseIndex && <Badge className="bg-blue-500 text-white">Current</Badge>}
                      {completedSets[index] && completedSets[index].length === Number.parseInt(exercise.sets) && (
                        <Badge className="bg-green-500 text-white">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Complete
                        </Badge>
                      )}
                      {completedSets[index] &&
                        completedSets[index].length > 0 &&
                        completedSets[index].length < Number.parseInt(exercise.sets) && (
                          <Badge variant="outline" className="border-orange-500 text-orange-700">
                            {completedSets[index].length}/{exercise.sets}
                          </Badge>
                        )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Exercise Video Modal */}
      <ExerciseVideoModal
        exercise={currentExercise}
        isOpen={isVideoModalOpen}
        onClose={() => setIsVideoModalOpen(false)}
      />
    </div>
  )
}
