"use client"

import { useState, useEffect, useCallback, memo } from 'react'

interface OptimizedTimerProps {
  isActive: boolean
  initialValue?: number
  countDown?: boolean
  onComplete?: () => void
  className?: string
  label?: string
}

const OptimizedTimer = memo(function OptimizedTimer({ 
  isActive, 
  initialValue = 0, 
  countDown = false, 
  onComplete,
  className = "",
  label = ""
}: OptimizedTimerProps) {
  const [time, setTime] = useState(initialValue)

  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }, [])

  useEffect(() => {
    if (!isActive) return

    const interval = setInterval(() => {
      setTime(prevTime => {
        const newTime = countDown ? prevTime - 1 : prevTime + 1
        
        if (countDown && newTime <= 0) {
          onComplete?.()
          return 0
        }
        
        return newTime
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [isActive, countDown, onComplete])

  // Reset timer when initialValue changes
  useEffect(() => {
    setTime(initialValue)
  }, [initialValue])

  return (
    <div className={className}>
      {label && <p className="text-sm opacity-80 mb-1">{label}</p>}
      <div className="font-bold text-2xl">
        {formatTime(time)}
      </div>
    </div>
  )
})

export { OptimizedTimer }
