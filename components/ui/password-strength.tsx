"use client"

import { PasswordValidationResult } from "@/lib/types"
import { PasswordValidator } from "@/lib/password-validator"
import { Check, X } from "lucide-react"

interface PasswordStrengthProps {
  password: string
  showRequirements?: boolean
}

export function PasswordStrength({ password, showRequirements = true }: PasswordStrengthProps) {
  const validation = PasswordValidator.validate(password)
  const requirements = PasswordValidator.getPasswordRequirements()

  const getStrengthBarColor = (strength: PasswordValidationResult['strength']) => {
    switch (strength) {
      case 'weak':
        return 'bg-red-500'
      case 'medium':
        return 'bg-yellow-500'
      case 'strong':
        return 'bg-green-500'
      default:
        return 'bg-gray-300'
    }
  }

  const getStrengthBarWidth = (strength: PasswordValidationResult['strength']) => {
    switch (strength) {
      case 'weak':
        return 'w-1/3'
      case 'medium':
        return 'w-2/3'
      case 'strong':
        return 'w-full'
      default:
        return 'w-0'
    }
  }

  if (!password) {
    return null
  }

  return (
    <div className="space-y-3">
      {/* Strength Bar */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">Password Strength</span>
          <span className={`text-sm font-medium ${PasswordValidator.getStrengthColor(validation.strength)}`}>
            {PasswordValidator.getStrengthText(validation.strength)}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getStrengthBarColor(validation.strength)} ${getStrengthBarWidth(validation.strength)}`}
          />
        </div>
      </div>

      {/* Requirements List */}
      {showRequirements && (
        <div className="space-y-2">
          <span className="text-sm font-medium text-gray-700">Requirements:</span>
          <ul className="space-y-1">
            {requirements.map((requirement, index) => {
              const isLengthReq = requirement.includes('characters long')
              const isUpperReq = requirement.includes('uppercase')
              const isLowerReq = requirement.includes('lowercase')
              const isNumberReq = requirement.includes('number')
              const isSpecialReq = requirement.includes('special character')
              const isPatternReq = requirement.includes('common patterns')

              let isMet = false
              if (isLengthReq) {
                isMet = password.length >= 8
              } else if (isUpperReq) {
                isMet = /[A-Z]/.test(password)
              } else if (isLowerReq) {
                isMet = /[a-z]/.test(password)
              } else if (isNumberReq) {
                isMet = /\d/.test(password)
              } else if (isSpecialReq) {
                isMet = /[!@#$%^&*(),.?":{}|<>]/.test(password)
              } else if (isPatternReq) {
                isMet = !/(.)\1{2,}/.test(password) && !/123456|654321|abcdef|qwerty|password/i.test(password)
              }

              return (
                <li key={index} className="flex items-center space-x-2 text-sm">
                  {isMet ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : (
                    <X className="h-4 w-4 text-red-600" />
                  )}
                  <span className={isMet ? 'text-green-600' : 'text-red-600'}>
                    {requirement}
                  </span>
                </li>
              )
            })}
          </ul>
        </div>
      )}

      {/* Validation Errors */}
      {validation.errors.length > 0 && (
        <div className="space-y-1">
          {validation.errors.map((error, index) => (
            <p key={index} className="text-sm text-red-600">
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  )
}
