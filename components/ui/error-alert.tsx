"use client"

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { AlertTriangle, X } from "lucide-react"
import { AppError } from "@/lib/types"

interface ErrorAlertProps {
  error: AppError
  onDismiss?: () => void
  className?: string
}

export function ErrorAlert({ error, onDismiss, className }: ErrorAlertProps) {
  const getErrorIcon = () => {
    switch (error.type) {
      case 'validation':
        return <AlertTriangle className="h-4 w-4" />
      case 'storage':
        return <AlertTriangle className="h-4 w-4" />
      case 'network':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getErrorTitle = () => {
    switch (error.type) {
      case 'validation':
        return 'Validation Error'
      case 'storage':
        return 'Storage Error'
      case 'network':
        return 'Network Error'
      default:
        return 'Error'
    }
  }

  return (
    <Alert variant="destructive" className={className}>
      {getErrorIcon()}
      <div className="flex-1">
        <AlertTitle>{getErrorTitle()}</AlertTitle>
        <AlertDescription>{error.message}</AlertDescription>
      </div>
      {onDismiss && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onDismiss}
          className="h-auto p-1 text-destructive hover:text-destructive"
        >
          <X className="h-4 w-4" />
        </Button>
      )}
    </Alert>
  )
}
