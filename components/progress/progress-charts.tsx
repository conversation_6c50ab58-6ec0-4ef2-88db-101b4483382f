"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from "recharts"
import { Plus, Weight, TrendingUp, TrendingDown, Minus, Edit, Trash2 } from "lucide-react"
import { useState } from "react"
import { useIsMobile } from "@/hooks/use-mobile"

interface ProgressChartsProps {
  selectedMetric: string | null
  onMetricSelect: (metric: string) => void
  onAddDataPoint: () => void
  getMetricData: (metric: string) => any[]
  getMetricConfig: (metric: string) => any
}

export function ProgressCharts({
  selectedMetric,
  onMetricSelect,
  onAddDataPoint,
  getMetricData,
  getMetricConfig
}: ProgressChartsProps) {
  const weightData = getMetricData("Weight")
  const isMobile = useIsMobile()

  // Calculate weight statistics
  const getWeightStats = () => {
    if (weightData.length === 0) return null

    const latest = weightData[weightData.length - 1]
    const previous = weightData.length > 1 ? weightData[weightData.length - 2] : null
    const first = weightData[0]

    const change = previous ? latest.value - previous.value : 0
    const totalChange = latest.value - first.value

    return {
      current: latest.value,
      change,
      totalChange,
      entries: weightData.length
    }
  }

  const stats = getWeightStats()

  // Mobile-optimized dot component for better touch interaction
  const CustomDot = (props: any) => {
    const { cx, cy } = props
    const dotRadius = isMobile ? 8 : 6
    const strokeWidth = isMobile ? 4 : 3

    return (
      <circle
        cx={cx}
        cy={cy}
        r={dotRadius}
        fill="#3B82F6"
        stroke="white"
        strokeWidth={strokeWidth}
      />
    )
  }

  return (
    <Card className="bg-white/90 backdrop-blur-sm shadow-xl border-0">
      <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-t-lg">
        <div className={`flex items-center justify-between ${isMobile ? 'flex-col gap-4' : ''}`}>
          <div className={isMobile ? 'text-center' : ''}>
            <CardTitle className={`font-bold flex items-center ${
              isMobile ? 'text-xl justify-center' : 'text-2xl'
            }`}>
              <Weight className={`mr-2 ${isMobile ? 'h-5 w-5' : 'h-6 w-6'}`} />
              Weight Progress
            </CardTitle>
            <CardDescription className={`text-blue-100 ${
              isMobile ? 'text-sm mt-1' : ''
            }`}>
              Track your weight loss journey over time
            </CardDescription>
          </div>
          <Button
            onClick={onAddDataPoint}
            size={isMobile ? "default" : "lg"}
            className={`bg-white/20 hover:bg-white/30 text-white border-white/40 font-semibold ${
              isMobile ? 'w-full min-h-[48px] text-base' : ''
            }`}
          >
            <Plus className={`mr-2 ${isMobile ? 'h-5 w-5' : 'h-5 w-5'}`} />
            Log Weight
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        {/* Weight Statistics */}
        {stats && (
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-6">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 sm:p-6 rounded-xl">
              <div className="text-sm sm:text-base font-medium text-blue-700 mb-2">Current Weight</div>
              <div className="text-2xl sm:text-3xl font-bold text-blue-900">{stats.current} lbs</div>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 sm:p-6 rounded-xl">
              <div className="text-sm sm:text-base font-medium text-green-700 mb-2">Total Weight Lost</div>
              <div className="text-2xl sm:text-3xl font-bold text-green-900">
                {Math.abs(stats.totalChange).toFixed(1)} lbs
              </div>
            </div>
          </div>
        )}

        {/* Weight Chart */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold text-gray-800">Weight Trend</h3>
            <Badge variant="outline" className="text-sm">
              <TrendingUp className="h-4 w-4 mr-1" />
              {weightData.length} entries
            </Badge>
          </div>

          {weightData.length === 0 ? (
            <div className={`text-center text-gray-500 bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl ${
              isMobile ? 'py-12 px-4' : 'py-16'
            }`}>
              <Weight className={`mx-auto mb-4 opacity-50 ${
                isMobile ? 'h-12 w-12' : 'h-16 w-16'
              }`} />
              <h4 className={`font-semibold mb-2 ${
                isMobile ? 'text-lg' : 'text-xl'
              }`}>Start Tracking Your Weight</h4>
              <p className={`mb-4 ${
                isMobile ? 'text-base' : 'text-lg'
              }`}>Log your first weight entry to begin your journey</p>
              <Button
                onClick={onAddDataPoint}
                size={isMobile ? "default" : "lg"}
                className={`bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold ${
                  isMobile ? 'w-full max-w-xs min-h-[48px]' : ''
                }`}
              >
                <Plus className="h-5 w-5 mr-2" />
                Add Your First Weight
              </Button>
            </div>
          ) : (
            <div
              className={`w-full bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl ${
                isMobile ? 'h-[420px] p-3' : 'h-96 p-4'
              }`}
              role="img"
              aria-label="Weight progress chart"
            >
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={weightData}
                  margin={
                    isMobile
                      ? { top: 15, right: 15, left: 15, bottom: 35 }
                      : { top: 20, right: 30, left: 20, bottom: 20 }
                  }
                >
                  <defs>
                    <linearGradient id="weightGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.4} />
                      <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0.1} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke="#E5E7EB"
                    opacity={isMobile ? 0.5 : 1}
                  />
                  <XAxis
                    dataKey="date"
                    stroke="#6B7280"
                    fontSize={isMobile ? 11 : 14}
                    tick={{ fontSize: isMobile ? 11 : 14 }}
                    tickMargin={isMobile ? 10 : 5}
                    height={isMobile ? 35 : 30}
                    interval={isMobile && weightData.length > 5 ? 'preserveStartEnd' : 0}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return isMobile
                        ? date.toLocaleDateString('en-US', {
                            month: 'numeric',
                            day: 'numeric'
                          })
                        : date.toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric'
                          });
                    }}
                  />
                  <YAxis
                    stroke="#6B7280"
                    fontSize={isMobile ? 12 : 14}
                    tick={{ fontSize: isMobile ? 12 : 14 }}
                    tickMargin={isMobile ? 8 : 5}
                    domain={['dataMin - 5', 'dataMax + 5']}
                    width={isMobile ? 45 : 60}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'white',
                      border: '2px solid #3B82F6',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',
                      fontSize: isMobile ? '14px' : '16px',
                      fontWeight: '500',
                      padding: isMobile ? '8px 12px' : '12px 16px',
                      minWidth: isMobile ? '120px' : 'auto'
                    }}
                    formatter={(value: any) => [`${value} lbs`, 'Weight']}
                    labelFormatter={(label) => `Date: ${label}`}
                  />
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke="#3B82F6"
                    strokeWidth={isMobile ? 3 : 4}
                    fill="url(#weightGradient)"
                    dot={<CustomDot />}
                    activeDot={{
                      r: isMobile ? 10 : 8,
                      stroke: '#3B82F6',
                      strokeWidth: isMobile ? 4 : 3,
                      fill: 'white'
                    }}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
