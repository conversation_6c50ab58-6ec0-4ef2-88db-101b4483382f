"use client"

import React, { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import {
  Weight,
  Camera,
  Plus,
  Calendar,
  User
} from "lucide-react"
import { ProgressCheckinDashboardProps } from "@/lib/types"
import { WeightService, type WeightEntry } from "@/lib/services/weight-service"
import { WorkoutService } from "@/lib/services/workout-service"
import { ProgressCharts } from "@/components/progress/progress-charts"
import { PhotoService } from "@/lib/services/photo-service"
import { EditWeightModal } from "@/components/modals/edit-weight-modal"
import { DeleteWeightModal } from "@/components/modals/delete-weight-modal"

export function ProgressCheckinDashboard({ user }: ProgressCheckinDashboardProps) {
  // Real data state
  const [weightEntries, setWeightEntries] = useState<WeightEntry[]>([])
  const [weightProgress, setWeightProgress] = useState<any>(null)
  const [workoutStats, setWorkoutStats] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  // State for modals and data entry
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedMetric, setSelectedMetric] = useState<string | null>(null)
  const [newDataPoint, setNewDataPoint] = useState({ value: "", date: "" })
  const [isAddingData, setIsAddingData] = useState(false)
  const [isUploadingPhoto, setIsUploadingPhoto] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [selectedPhotoType, setSelectedPhotoType] = useState<'front' | 'side' | 'back' | 'progress'>('progress')
  const [monthlyPhotoProgress, setMonthlyPhotoProgress] = useState<{
    front: boolean
    side: boolean
    back: boolean
    total: number
  }>({ front: false, side: false, back: false, total: 0 })
  const [userPhotos, setUserPhotos] = useState<any[]>([])
  const [loadingPhotos, setLoadingPhotos] = useState(false)
  const [showPhotoGuidelines, setShowPhotoGuidelines] = useState(false)
  const [showWeightHistory, setShowWeightHistory] = useState(false)

  // Photo filtering and comparison states
  const [photoFilter, setPhotoFilter] = useState<'all' | 'front' | 'side' | 'back' | 'progress'>('all')
  const [showComparison, setShowComparison] = useState(false)
  const [selectedPhotos, setSelectedPhotos] = useState<any[]>([])
  const [editingPhoto, setEditingPhoto] = useState<any>(null)
  const [showPhotoTypeSelector, setShowPhotoTypeSelector] = useState(false)

  // Weight entry management state
  const [editWeightModal, setEditWeightModal] = useState<{
    isOpen: boolean
    date: string
    weight: number
  }>({ isOpen: false, date: "", weight: 0 })

  const [deleteWeightModal, setDeleteWeightModal] = useState<{
    isOpen: boolean
    date: string
    weight: number
  }>({ isOpen: false, date: "", weight: 0 })

  // Load real data on component mount
  useEffect(() => {
    if (user?.id) {
      loadData()
    }
  }, [user?.id])

  const loadData = async () => {
    if (!user?.id) return

    try {
      setLoading(true)

      // Load weight entries
      const { data: weights } = await WeightService.getUserWeightEntries(user.id)
      setWeightEntries(weights || [])

      // Load weight progress (30 days)
      const { data: progress } = await WeightService.getWeightProgress(user.id, 30)
      setWeightProgress(progress)

      // Load workout stats (30 days)
      const { data: stats } = await WorkoutService.getWorkoutStats(user.id, 30)
      setWorkoutStats(stats)

      // Load user photos
      await loadUserPhotos()

    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadUserPhotos = async () => {
    try {
      setLoadingPhotos(true)
      const { data: photos, error } = await PhotoService.getUserPhotos(user.id)

      if (error) {
        console.error('Error loading photos:', error)
        return
      }

      setUserPhotos(photos || [])

      // Calculate monthly progress for simplified photo tracking
      const currentMonth = new Date().toISOString().slice(0, 7) // YYYY-MM format
      const thisMonthPhotos = (photos || []).filter((photo: any) =>
        photo.upload_date.startsWith(currentMonth)
      )

      // Simplified progress tracking - just count total photos this month
      const progress = {
        front: false, // Legacy - no longer used
        side: false,  // Legacy - no longer used
        back: false,  // Legacy - no longer used
        total: thisMonthPhotos.length
      }
      setMonthlyPhotoProgress(progress)

    } catch (error) {
      console.error('Error loading user photos:', error)
    } finally {
      setLoadingPhotos(false)
    }
  }

  // Helper function to get metric data
  const getMetricData = (metric: string) => {
    switch (metric) {
      case "Weight":
        // Sort by date ascending for proper timeline visualization (oldest to newest)
        return weightEntries
          .sort((a, b) => new Date(a.recorded_date).getTime() - new Date(b.recorded_date).getTime())
          .map(entry => ({
            date: entry.recorded_date,
            value: entry.weight_value
          }))
      case "Workouts":
        return [] // TODO: Implement workout data format if needed
      default:
        return []
    }
  }

  // Handle adding new weight data
  const handleAddDataPoint = async () => {
    setIsAddingData(true)
    try {
      if (selectedMetric === "Weight" && newDataPoint.value && newDataPoint.date) {
        const weightValue = parseFloat(newDataPoint.value)

        // Validate weight value
        if (isNaN(weightValue) || weightValue <= 0 || weightValue > 1000) {
          alert("Invalid weight value. Please enter a valid weight between 0 and 1000 lbs.")
          return
        }

        const { error } = await WeightService.addWeightEntry({
          user_id: user.id,
          weight_value: weightValue,
          unit: 'lbs',
          recorded_date: newDataPoint.date
        })

        if (!error) {
          alert(`Weight logged successfully! 🎉`)
          // Reload data to show updated values
          await loadData()
        } else {
          if (error.code === '23505') {
            alert("You already have a weight entry for this date. Please choose a different date.")
          } else {
            alert("Failed to add weight entry. Please try again.")
          }
          return
        }
      } else {
        alert("Please enter both a weight value and date.")
        return
      }
      setNewDataPoint({ value: "", date: "" })
      setIsModalOpen(false)
    } catch (error) {
      alert("An error occurred while adding the weight entry.")
    } finally {
      setIsAddingData(false)
    }
  }

  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !user?.id || !selectedPhotoType) return

    try {
      setIsUploadingPhoto(true)

      const uploadDate = new Date().toISOString().split('T')[0]
      const { data, error } = await PhotoService.uploadPhoto(
        user.id,
        file,
        selectedPhotoType,
        uploadDate,
        `${selectedPhotoType.charAt(0).toUpperCase() + selectedPhotoType.slice(1)} photo uploaded from dashboard`
      )

      if (error) {
        alert(`Error uploading photo: ${error.message}`)
      } else {
        alert('Progress photo uploaded successfully!')
        // Refresh photo display
        await loadUserPhotos()
      }
    } catch (error) {
      console.error('Error uploading photo:', error)
      alert('An error occurred while uploading the photo.')
    } finally {
      setIsUploadingPhoto(false)
      // Reset the input
      event.target.value = ''
    }
  }

  // Function no longer needed since we removed quick upload

  const handlePhotoTypeSelect = (type: 'front' | 'side' | 'back' | 'progress') => {
    setSelectedPhotoType(type)
    setShowPhotoTypeSelector(false)
    fileInputRef.current?.click()
  }

  // Filter photos based on selected filter
  const getFilteredPhotos = () => {
    if (photoFilter === 'all') {
      return userPhotos
    }
    return userPhotos.filter(photo => photo.photo_type === photoFilter)
  }

  // Photo comparison functions
  const handlePhotoSelect = (photo: any) => {
    if (showComparison) {
      if (selectedPhotos.length < 2 && !selectedPhotos.find(p => p.id === photo.id)) {
        setSelectedPhotos([...selectedPhotos, photo])
      }
    }
  }

  const clearComparison = () => {
    setSelectedPhotos([])
    setShowComparison(false)
  }

  const startComparison = () => {
    setSelectedPhotos([])
    setShowComparison(true)
  }

  // Photo editing functions
  const handleEditPhoto = (photo: any) => {
    setEditingPhoto(photo)
  }

  const handleSavePhotoEdit = async (photoId: string, updates: { notes?: string, photo_type?: string }) => {
    try {
      const { error } = await PhotoService.updatePhoto(photoId, updates)
      if (error) {
        alert(`Error updating photo: ${error.message}`)
      } else {
        alert('Photo updated successfully!')
        setEditingPhoto(null)
        await loadUserPhotos()
      }
    } catch (error) {
      console.error('Error updating photo:', error)
      alert('An error occurred while updating the photo.')
    }
  }

  const handleDeletePhoto = async (photoId: string, photoType: string) => {
    if (!confirm(`Are you sure you want to delete this ${photoType} photo? This action cannot be undone.`)) {
      return
    }

    try {
      const { error } = await PhotoService.deletePhoto(photoId)
      if (error) {
        alert(`Error deleting photo: ${error.message}`)
      } else {
        alert('Photo deleted successfully!')
        await loadUserPhotos()
      }
    } catch (error) {
      console.error('Error deleting photo:', error)
      alert('An error occurred while deleting the photo.')
    }
  }

  // Weight entry management handlers
  const handleEditWeight = (date: string, weight: number) => {
    try {
      // Validate that the entry exists
      const entryExists = weightEntries.find(
        entry => entry.recorded_date === date && entry.weight_value === weight
      )

      if (!entryExists) {
        alert('Weight entry not found. Please refresh the page and try again.')
        return
      }

      setEditWeightModal({ isOpen: true, date, weight })
    } catch (error) {
      console.error('Error opening edit modal:', error)
      alert('Unable to edit weight entry. Please try again.')
    }
  }

  const handleDeleteWeight = (date: string, weight: number) => {
    try {
      // Validate that the entry exists
      const entryExists = weightEntries.find(
        entry => entry.recorded_date === date && entry.weight_value === weight
      )

      if (!entryExists) {
        alert('Weight entry not found. Please refresh the page and try again.')
        return
      }

      setDeleteWeightModal({ isOpen: true, date, weight })
    } catch (error) {
      console.error('Error opening delete modal:', error)
      alert('Unable to delete weight entry. Please try again.')
    }
  }

  const handleWeightOperationSuccess = async () => {
    try {
      // Reload all weight data to ensure real-time updates
      await loadData()
    } catch (error) {
      console.error('Error refreshing data after weight operation:', error)
      // Still close modals even if refresh fails
    }
  }

  return (
    <>
      <div className="space-y-8">
        {/* Weight Progress Chart */}
        <ProgressCharts
          selectedMetric="Weight"
          onMetricSelect={() => {}}
          onAddDataPoint={() => {
            setSelectedMetric("Weight")
            setNewDataPoint({ value: "", date: new Date().toISOString().split('T')[0] })
            setIsModalOpen(true)
          }}
          getMetricData={getMetricData}
          getMetricConfig={() => ({})}
        />

        {/* Weight History Section */}
        <Card className="bg-white/90 backdrop-blur-sm shadow-xl border-0">
          <CardHeader className="bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-t-lg">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl font-bold flex items-center">
                  📊 Weight History
                </CardTitle>
                <CardDescription className="text-gray-100">
                  View and manage all your weight entries
                </CardDescription>
              </div>
              <Button
                onClick={() => setShowWeightHistory(!showWeightHistory)}
                variant="ghost"
                className="text-white hover:bg-white/20 text-lg font-semibold"
              >
                {showWeightHistory ? 'Hide History' : 'Show History'}
              </Button>
            </div>
          </CardHeader>

          {showWeightHistory && (
            <CardContent className="p-4 sm:p-6">
              {weightEntries.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p className="text-lg mb-4">No weight entries yet</p>
                  <Button
                    onClick={() => {
                      setSelectedMetric("Weight")
                      setNewDataPoint({ value: "", date: new Date().toISOString().split('T')[0] })
                      setIsModalOpen(true)
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Add Your First Weight Entry
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="text-sm text-gray-600 mb-4">
                    Total entries: {weightEntries.length}
                  </div>

                  {/* Mobile-friendly weight entries */}
                  <div className="space-y-3">
                    {weightEntries
                      .sort((a, b) => new Date(b.recorded_date).getTime() - new Date(a.recorded_date).getTime())
                      .map((entry, index) => (
                        <div key={entry.id || index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                            <div className="flex-1">
                              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                                <div className="font-semibold text-lg text-gray-900">
                                  {entry.weight_value} lbs
                                </div>
                                <div className="text-gray-600">
                                  {new Date(entry.recorded_date).toLocaleDateString('en-US', {
                                    weekday: 'short',
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric'
                                  })}
                                </div>
                              </div>
                              {entry.notes && (
                                <div className="text-sm text-gray-600 mt-2 italic">
                                  "{entry.notes}"
                                </div>
                              )}
                            </div>

                            <div className="flex gap-2 sm:gap-3">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEditWeight(entry.recorded_date, entry.weight_value)}
                                className="flex-1 sm:flex-none bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700 font-medium"
                              >
                                ✏️ Edit
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDeleteWeight(entry.recorded_date, entry.weight_value)}
                                className="flex-1 sm:flex-none bg-red-50 hover:bg-red-100 border-red-200 text-red-700 font-medium"
                              >
                                🗑️ Delete
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </CardContent>
          )}
        </Card>

        {/* Progress Photos Section */}
        <Card className="bg-white/90 backdrop-blur-sm shadow-xl border-0">
          <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-t-lg">
            <CardTitle className="text-2xl font-bold flex items-center">
              <Camera className="h-6 w-6 mr-2" />
              Progress Photos
            </CardTitle>
            <CardDescription className="text-blue-100">
              Visual documentation of your transformation journey
            </CardDescription>
          </CardHeader>
          <CardContent className="p-4 sm:p-8">
            {/* Collapsible Photo Guidelines */}
            <div className="mb-6">
              <Button
                variant="ghost"
                onClick={() => setShowPhotoGuidelines(!showPhotoGuidelines)}
                className="w-full justify-between p-3 h-auto bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg"
              >
                <span className="font-semibold text-blue-900 text-lg">💡 Photo Tips</span>
                <span className="text-blue-600 text-xl">
                  {showPhotoGuidelines ? '▼' : '▶'}
                </span>
              </Button>

              {showPhotoGuidelines && (
                <div className="mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="space-y-3 text-base text-blue-800">
                    <p className="font-semibold text-lg">For best results:</p>
                    <ul className="list-disc list-inside space-y-2 ml-4 text-base">
                      <li>Take photos in good lighting</li>
                      <li>Wear similar clothing each time</li>
                      <li>Use the same location and background</li>
                      <li>Take photos at the same time of day</li>
                    </ul>
                  </div>
                </div>
              )}
            </div>

            {/* Monthly Progress Indicator - Simplified */}
            {userPhotos.length > 0 && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-green-900 text-lg">This Month's Progress</h4>
                  <span className="text-base text-green-700 font-medium">
                    {userPhotos.filter(photo => {
                      const photoDate = new Date(photo.upload_date)
                      const currentDate = new Date()
                      return photoDate.getMonth() === currentDate.getMonth() &&
                             photoDate.getFullYear() === currentDate.getFullYear()
                    }).length} photos uploaded
                  </span>
                </div>
              </div>
            )}

            {/* Enhanced Photo Upload */}
            <div className="text-center mb-8">
              <div className="p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl mb-6 inline-block">
                <Camera className="h-16 w-16 text-blue-400 mx-auto" />
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                Upload Progress Photo
              </h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto text-lg">
                Take a photo to track your progress over time.
              </p>

              <input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/png,image/webp"
                onChange={handlePhotoUpload}
                className="hidden"
                disabled={isUploadingPhoto}
              />

              {!showPhotoTypeSelector ? (
                <Button
                  size="lg"
                  onClick={() => setShowPhotoTypeSelector(true)}
                  disabled={isUploadingPhoto}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-10 rounded-xl text-lg h-auto disabled:opacity-50"
                >
                  <Camera className="h-6 w-6 mr-3" />
                  📸 Upload Progress Photo
                </Button>
              ) : (
                <div className="space-y-6">
                  <h4 className="text-xl font-semibold text-gray-900">Choose Photo Type</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-2xl mx-auto">
                    <Button
                      onClick={() => handlePhotoTypeSelect('front')}
                      className="h-24 flex flex-col items-center justify-center space-y-2 bg-blue-50 hover:bg-blue-100 border-2 border-blue-200 text-blue-800 font-semibold rounded-xl"
                    >
                      <span className="text-3xl">🧍</span>
                      <span>Front View</span>
                    </Button>

                    <Button
                      onClick={() => handlePhotoTypeSelect('side')}
                      className="h-24 flex flex-col items-center justify-center space-y-2 bg-green-50 hover:bg-green-100 border-2 border-green-200 text-green-800 font-semibold rounded-xl"
                    >
                      <span className="text-3xl">🚶</span>
                      <span>Side View</span>
                    </Button>

                    <Button
                      onClick={() => handlePhotoTypeSelect('back')}
                      className="h-24 flex flex-col items-center justify-center space-y-2 bg-purple-50 hover:bg-purple-100 border-2 border-purple-200 text-purple-800 font-semibold rounded-xl"
                    >
                      <span className="text-3xl">🚶‍♂️</span>
                      <span>Back View</span>
                    </Button>
                  </div>

                  <Button
                    onClick={() => setShowPhotoTypeSelector(false)}
                    variant="ghost"
                    className="text-gray-600 text-lg"
                  >
                    ← Back to Quick Upload
                  </Button>
                </div>
              )}
            </div>

            {/* Enhanced Photo Gallery with Filtering and Comparison */}
            {userPhotos.length > 0 && (
              <div className="mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-gray-200">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
                  <h4 className="text-xl font-semibold text-gray-900">Your Progress Photos</h4>

                  {/* Filter and Comparison Controls */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    {/* Photo Filter */}
                    <div className="flex flex-wrap gap-2">
                      {[
                        { key: 'all', label: 'All Photos', icon: '📸' },
                        { key: 'front', label: 'Front', icon: '🧍' },
                        { key: 'side', label: 'Side', icon: '🚶' },
                        { key: 'back', label: 'Back', icon: '🚶‍♂️' }
                      ].map((filter) => (
                        <Button
                          key={filter.key}
                          onClick={() => setPhotoFilter(filter.key as any)}
                          variant={photoFilter === filter.key ? "default" : "outline"}
                          size="sm"
                          className={`text-sm font-medium ${
                            photoFilter === filter.key
                              ? 'bg-blue-600 text-white'
                              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          <span className="mr-1">{filter.icon}</span>
                          {filter.label}
                        </Button>
                      ))}
                    </div>

                    {/* Comparison Toggle */}
                    {!showComparison ? (
                      <Button
                        onClick={startComparison}
                        variant="outline"
                        size="sm"
                        className="border-green-300 text-green-700 hover:bg-green-50 font-medium"
                      >
                        🔍 Compare Photos
                      </Button>
                    ) : (
                      <div className="flex gap-2">
                        <span className="text-sm text-gray-600 py-2">
                          Select 2 photos to compare ({selectedPhotos.length}/2)
                        </span>
                        <Button
                          onClick={clearComparison}
                          variant="outline"
                          size="sm"
                          className="border-red-300 text-red-700 hover:bg-red-50"
                        >
                          Cancel
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                  {getFilteredPhotos()
                    .sort((a, b) => new Date(b.upload_date).getTime() - new Date(a.upload_date).getTime())
                    .map((photo, index) => {
                      const isSelected = selectedPhotos.find(p => p.id === photo.id)
                      const canSelect = showComparison && selectedPhotos.length < 2

                      return (
                      <div
                        key={photo.id || index}
                        className={`bg-white rounded-lg shadow-md overflow-hidden border-2 transition-all ${
                          isSelected
                            ? 'border-green-500 ring-2 ring-green-200'
                            : showComparison && canSelect
                              ? 'border-blue-300 hover:border-blue-500 cursor-pointer'
                              : 'border-gray-200'
                        }`}
                        onClick={() => showComparison && canSelect ? handlePhotoSelect(photo) : undefined}
                      >
                        <div className="aspect-square bg-gray-200 overflow-hidden">
                          <img
                            src={`${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/progress-photos/${photo.storage_path}`}
                            alt="Progress photo"
                            className="w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity"
                            onError={(e) => {
                              e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgNzBDOTQuNDc3MiA3MCA5MCA3NC40NzcyIDkwIDgwVjEyMEM5MCA5NC40NzcyIDk0LjQ3NzIgOTAgMTAwIDkwSDEwMEMxMDUuNTIzIDkwIDExMCA5NC40NzcyIDExMCAxMDBWMTIwQzExMCAxMjUuNTIzIDEwNS41MjMgMTMwIDEwMCAxMzBIOTBWMTQwSDExMEMxMTUuNTIzIDE0MCAxMjAgMTM1LjUyMyAxMjAgMTMwVjEwMEMxMjAgODQuNDc3MiAxMDUuNTIzIDcwIDEwMCA3MFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'
                            }}
                            onClick={() => {
                              window.open(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/progress-photos/${photo.storage_path}`, '_blank')
                            }}
                          />
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="text-base font-medium text-gray-900">
                              {new Date(photo.upload_date).toLocaleDateString('en-US', {
                                weekday: 'short',
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                            </div>
                            {/* Photo Type Badge */}
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              photo.photo_type === 'front' ? 'bg-blue-100 text-blue-800' :
                              photo.photo_type === 'side' ? 'bg-green-100 text-green-800' :
                              photo.photo_type === 'back' ? 'bg-purple-100 text-purple-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {photo.photo_type === 'front' ? '🧍 Front' :
                               photo.photo_type === 'side' ? '🚶 Side' :
                               photo.photo_type === 'back' ? '🚶‍♂️ Back' :
                               '📸 Progress'}
                            </span>
                          </div>

                          {photo.notes && (
                            <p className="text-sm text-gray-600 mb-3 italic">"{photo.notes}"</p>
                          )}

                          {/* Selection indicator for comparison mode */}
                          {showComparison && isSelected && (
                            <div className="mb-3 p-2 bg-green-50 border border-green-200 rounded-lg">
                              <span className="text-sm font-medium text-green-800">
                                ✓ Selected for comparison
                              </span>
                            </div>
                          )}

                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1 bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700 font-medium"
                              onClick={(e) => {
                                e.stopPropagation()
                                window.open(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/progress-photos/${photo.storage_path}`, '_blank')
                              }}
                            >
                              👁️ View
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="bg-yellow-50 hover:bg-yellow-100 border-yellow-200 text-yellow-700 font-medium px-3"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleEditPhoto(photo)
                              }}
                            >
                              ✏️
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="bg-red-50 hover:bg-red-100 border-red-200 text-red-700 font-medium px-3"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleDeletePhoto(photo.id, photo.photo_type)
                              }}
                            >
                              🗑️
                            </Button>
                          </div>
                        </div>
                      </div>
                      )
                    })}
                </div>
                {/* Photo Comparison View */}
                {selectedPhotos.length === 2 && (
                  <div className="mt-8 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border-2 border-green-200">
                    <h5 className="text-xl font-semibold text-gray-900 mb-6 text-center">
                      📊 Photo Comparison
                    </h5>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {selectedPhotos.map((photo, index) => (
                        <div key={photo.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                          <div className="aspect-square bg-gray-200 overflow-hidden">
                            <img
                              src={`${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/progress-photos/${photo.storage_path}`}
                              alt={`Comparison photo ${index + 1}`}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgNzBDOTQuNDc3MiA3MCA5MCA3NC40NzcyIDkwIDgwVjEyMEM5MCA5NC40NzcyIDk0LjQ3NzIgOTAgMTAwIDkwSDEwMEMxMDUuNTIzIDkwIDExMCA5NC40NzcyIDExMCAxMDBWMTIwQzExMCAxMjUuNTIzIDEwNS41MjMgMTMwIDEwMCAxMzBIOTBWMTQwSDExMEMxMTUuNTIzIDE0MCAxMjAgMTM1LjUyMyAxMjAgMTMwVjEwMEMxMjAgODQuNDc3MiAxMDUuNTIzIDcwIDEwMCA3MFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'
                              }}
                            />
                          </div>
                          <div className="p-4 text-center">
                            <div className="font-semibold text-gray-900 mb-1">
                              {new Date(photo.upload_date).toLocaleDateString('en-US', {
                                weekday: 'short',
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                            </div>
                            <div className="text-sm text-gray-600">
                              {photo.photo_type === 'front' ? '🧍 Front View' :
                               photo.photo_type === 'side' ? '🚶 Side View' :
                               photo.photo_type === 'back' ? '🚶‍♂️ Back View' :
                               '📸 Progress Photo'}
                            </div>
                            {photo.notes && (
                              <p className="text-sm text-gray-600 mt-2 italic">"{photo.notes}"</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="mt-6 text-center">
                      <Button
                        onClick={clearComparison}
                        className="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-8 rounded-xl"
                      >
                        ✨ Start New Comparison
                      </Button>
                    </div>
                  </div>
                )}

                <div className="mt-6 text-center">
                  <p className="text-gray-600 text-lg">
                    Total photos: <span className="font-semibold">{getFilteredPhotos().length}</span>
                    {photoFilter !== 'all' && (
                      <span className="text-sm text-gray-500 ml-2">
                        ({userPhotos.length} total)
                      </span>
                    )}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Weight Data Entry Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold flex items-center">
              <Weight className="h-5 w-5 mr-2 text-blue-600" />
              Log Your Weight
            </DialogTitle>
            <DialogDescription>
              Enter your current weight to track your progress over time
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            <div>
              <Label htmlFor="weight-value" className="text-base font-medium text-gray-700 mb-2 block">
                Weight (lbs)
              </Label>
              <Input
                id="weight-value"
                type="number"
                step="0.1"
                placeholder="e.g., 150.5"
                value={newDataPoint.value}
                onChange={(e) => setNewDataPoint({ ...newDataPoint, value: e.target.value })}
                className="text-lg py-3"
              />
            </div>

            <div>
              <Label htmlFor="weight-date" className="text-base font-medium text-gray-700 mb-2 block">
                Date
              </Label>
              <Input
                id="weight-date"
                type="date"
                value={newDataPoint.date}
                onChange={(e) => setNewDataPoint({ ...newDataPoint, date: e.target.value })}
                className="text-lg py-3"
              />
            </div>

            <Button
              onClick={handleAddDataPoint}
              disabled={!newDataPoint.value || !newDataPoint.date || isAddingData}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 text-lg rounded-xl"
            >
              <Plus className="h-5 w-5 mr-2" />
              {isAddingData ? "Saving..." : "Save Weight Entry"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Weight Modal */}
      <EditWeightModal
        isOpen={editWeightModal.isOpen}
        onClose={() => setEditWeightModal({ isOpen: false, date: "", weight: 0 })}
        onSuccess={handleWeightOperationSuccess}
        userId={user.id}
        initialDate={editWeightModal.date}
        initialWeight={editWeightModal.weight}
        weightEntries={weightEntries}
      />

      {/* Delete Weight Modal */}
      <DeleteWeightModal
        isOpen={deleteWeightModal.isOpen}
        onClose={() => setDeleteWeightModal({ isOpen: false, date: "", weight: 0 })}
        onSuccess={handleWeightOperationSuccess}
        userId={user.id}
        targetDate={deleteWeightModal.date}
        targetWeight={deleteWeightModal.weight}
        weightEntries={weightEntries}
      />

      {/* Photo Edit Modal */}
      {editingPhoto && (
        <Dialog open={!!editingPhoto} onOpenChange={() => setEditingPhoto(null)}>
          <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-xl font-semibold">Edit Photo Details</DialogTitle>
              <DialogDescription>
                Update the photo type and add notes to help track your progress.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Photo Preview */}
              <div className="aspect-square bg-gray-200 rounded-lg overflow-hidden max-w-xs mx-auto">
                <img
                  src={`${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/progress-photos/${editingPhoto.storage_path}`}
                  alt="Photo being edited"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgNzBDOTQuNDc3MiA3MCA5MCA3NC40NzcyIDkwIDgwVjEyMEM5MCA5NC40NzcyIDk0LjQ3NzIgOTAgMTAwIDkwSDEwMEMxMDUuNTIzIDkwIDExMCA5NC40NzcyIDExMCAxMDBWMTIwQzExMCAxMjUuNTIzIDEwNS41MjMgMTMwIDEwMCAxMzBIOTBWMTQwSDExMEMxMTUuNTIzIDE0MCAxMjAgMTM1LjUyMyAxMjAgMTMwVjEwMEMxMjAgODQuNDc3MiAxMDUuNTIzIDcwIDEwMCA3MFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'
                  }}
                />
              </div>

              {/* Photo Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Photo Type
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {[
                    { value: 'front', label: 'Front View', icon: '🧍' },
                    { value: 'side', label: 'Side View', icon: '🚶' },
                    { value: 'back', label: 'Back View', icon: '🚶‍♂️' },
                    { value: 'progress', label: 'Progress', icon: '📸' }
                  ].map((type) => (
                    <Button
                      key={type.value}
                      type="button"
                      variant={editingPhoto.photo_type === type.value ? "default" : "outline"}
                      className={`h-12 ${
                        editingPhoto.photo_type === type.value
                          ? 'bg-blue-600 text-white'
                          : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                      onClick={() => setEditingPhoto({...editingPhoto, photo_type: type.value})}
                    >
                      <span className="mr-2">{type.icon}</span>
                      {type.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes (Optional)
                </label>
                <textarea
                  value={editingPhoto.notes || ''}
                  onChange={(e) => setEditingPhoto({...editingPhoto, notes: e.target.value})}
                  placeholder="Add notes about this photo..."
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  rows={3}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setEditingPhoto(null)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={() => handleSavePhotoEdit(editingPhoto.id, {
                    photo_type: editingPhoto.photo_type,
                    notes: editingPhoto.notes
                  })}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Save Changes
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  )
}
