"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { useAuthContext } from '@/lib/auth-context'
import { supabase } from '@/lib/supabase'

export function AdminReset() {
  const { user } = useAuthContext()
  const [isResetting, setIsResetting] = useState(false)

  const resetOnboarding = async () => {
    if (!user) return

    setIsResetting(true)
    try {
      // Delete intake responses from database
      await supabase
        .from('intake_responses')
        .delete()
        .eq('user_id', user.id)

      // Remove the program from user metadata
      await supabase.auth.updateUser({
        data: {
          ...user.user_metadata,
          program: null
        }
      })

      // Refresh the page to restart the flow
      window.location.reload()
    } catch (error) {
      console.error('Error resetting onboarding:', error)
    } finally {
      setIsResetting(false)
    }
  }

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Button
        onClick={resetOnboarding}
        disabled={isResetting}
        variant="outline"
        size="sm"
        className="bg-red-50 border-red-200 text-red-700 hover:bg-red-100"
      >
        {isResetting ? 'Resetting...' : 'Reset Onboarding (Dev)'}
      </Button>
    </div>
  )
}
