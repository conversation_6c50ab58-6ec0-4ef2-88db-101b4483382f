"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { WeightService, type WeightEntry } from '@/lib/services/weight-service'
import { WorkoutService } from '@/lib/services/workout-service'
import { useAuthContext } from '@/lib/auth-context'
import { Scale, Activity, Target, TrendingUp, Calendar, Zap } from 'lucide-react'

interface MetricsOverviewProps {
  onAddWorkout: () => void
}

export function MetricsOverview({ onAddWorkout }: MetricsOverviewProps) {
  const { user } = useAuthContext()
  const [latestWeight, setLatestWeight] = useState<WeightEntry | null>(null)
  const [weightProgress, setWeightProgress] = useState<any>(null)
  const [workoutStats, setWorkoutStats] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user?.id) {
      loadMetrics()
    }
  }, [user?.id])

  const loadMetrics = async () => {
    if (!user?.id) return

    try {
      setLoading(true)
      // Load latest weight
      const { data: weight } = await WeightService.getLatestWeight(user.id)
      setLatestWeight(weight)

      // Load weight progress (30 days)
      const { data: progress } = await WeightService.getWeightProgress(user.id, 30)
      setWeightProgress(progress)

      // Load workout stats (30 days)
      const { data: stats } = await WorkoutService.getWorkoutStats(user.id, 30)
      setWorkoutStats(stats)

    } catch (error) {
      console.error('Error loading metrics:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
      {/* Current Weight */}
      <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle className="text-xs sm:text-sm font-medium text-blue-900">Current Weight</CardTitle>
          <Scale className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600" />
        </CardHeader>
        <CardContent className="pb-2 sm:pb-3 lg:pb-6">
          <div className="text-lg sm:text-xl lg:text-2xl font-bold text-blue-900">
            {latestWeight ? `${latestWeight.weight_value} ${latestWeight.unit}` : 'No data'}
          </div>
          <p className="text-xs text-blue-700 mt-1">
            {latestWeight ? `Recorded ${new Date(latestWeight.recorded_date).toLocaleDateString()}` : 'No weight entries yet'}
          </p>
        </CardContent>
      </Card>

      {/* Weekly Workouts */}
      <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle className="text-xs sm:text-sm font-medium text-green-900">This Week</CardTitle>
          <Activity className="h-3 w-3 sm:h-4 sm:w-4 text-green-600" />
        </CardHeader>
        <CardContent className="pb-2 sm:pb-3 lg:pb-6">
          <div className="text-lg sm:text-xl lg:text-2xl font-bold text-green-900">
            {workoutStats?.weeklyWorkouts || 0} workouts
          </div>
          <p className="text-xs text-green-700 mt-1">
            {workoutStats?.weeklyWorkouts > 0 ?
              'Great progress this week!' :
              'Start your first workout'
            }
          </p>
        </CardContent>
      </Card>

      {/* Workout Streak */}
      <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200 col-span-2 lg:col-span-1">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle className="text-xs sm:text-sm font-medium text-red-900">Current Streak</CardTitle>
          <Zap className="h-3 w-3 sm:h-4 sm:w-4 text-red-600" />
        </CardHeader>
        <CardContent className="pb-2 sm:pb-3 lg:pb-6">
          <div className="text-lg sm:text-xl lg:text-2xl font-bold text-red-900">
            {workoutStats?.streak || 0} days
          </div>
          <p className="text-xs text-red-700 mt-1">
            Keep it up! 🔥
          </p>
        </CardContent>
      </Card>

    </div>
  )
}
