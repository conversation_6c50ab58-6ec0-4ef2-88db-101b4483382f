"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { WeightService } from '@/lib/services/weight-service'
import { useAuthContext } from '@/lib/auth-context'
import { Scale, X } from 'lucide-react'

interface WeightEntryFormProps {
  onClose: () => void
  onSuccess: () => void
}

export function WeightEntryForm({ onClose, onSuccess }: WeightEntryFormProps) {
  const { user } = useAuthContext()
  const [weight, setWeight] = useState('')
  const [unit, setUnit] = useState<'lbs' | 'kg'>('lbs')
  const [date, setDate] = useState(new Date().toISOString().split('T')[0])
  const [notes, setNotes] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user?.id) return

    setLoading(true)
    setError('')

    try {
      const weightValue = parseFloat(weight)
      if (isNaN(weightValue) || weightValue <= 0) {
        setError('Please enter a valid weight')
        return
      }

      const { error: saveError } = await WeightService.addWeightEntry({
        user_id: user.id,
        weight_value: weightValue,
        unit,
        recorded_date: date,
        notes: notes.trim() || undefined
      })

      if (saveError) {
        if (saveError.code === '23505') { // Unique constraint violation
          setError('You already have a weight entry for this date. Please choose a different date.')
        } else {
          setError('Failed to save weight entry. Please try again.')
        }
        return
      }

      onSuccess()
      onClose()
    } catch (error) {
      console.error('Error saving weight:', error)
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Scale className="h-5 w-5 text-blue-600" />
              <CardTitle>Add Weight Entry</CardTitle>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription>
            Track your weight progress over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                {error}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="weight">Weight *</Label>
              <Input
                id="weight"
                type="number"
                step="0.1"
                min="0"
                max="1000"
                placeholder="Enter your weight"
                value={weight}
                onChange={(e) => setWeight(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label>Unit</Label>
              <RadioGroup value={unit} onValueChange={(value) => setUnit(value as 'lbs' | 'kg')}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="lbs" id="lbs" />
                  <Label htmlFor="lbs">Pounds (lbs)</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="kg" id="kg" />
                  <Label htmlFor="kg">Kilograms (kg)</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="date">Date *</Label>
              <Input
                id="date"
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                max={new Date().toISOString().split('T')[0]}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes (optional)</Label>
              <Textarea
                id="notes"
                placeholder="Any notes about this weigh-in..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose} className="flex-1">
                Cancel
              </Button>
              <Button type="submit" disabled={loading} className="flex-1">
                {loading ? 'Saving...' : 'Save Weight'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
