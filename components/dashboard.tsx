"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { WorkoutProgram } from "@/components/workout-program"
import { NutritionGuide } from "@/components/nutrition-guide"
import { Recommendations } from "@/components/recommendations"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { LogOut, BarChart3, <PERSON>mbbell, Apple, Settings, ShoppingCart } from "lucide-react"
import { MetricsOverview } from "@/components/dashboard/metrics-overview"
import { WeightEntryForm } from "@/components/dashboard/weight-entry-form"
import { ProgressCheckinDashboard } from "@/components/progress-checkin-dashboard"
import { SettingsPage } from "@/components/settings-page"
import { DashboardProps } from "@/lib/types"
import { SecureStorage } from "@/lib/secure-storage"
import { useAuthContext } from "@/lib/auth-context"

export function Dashboard({ user }: DashboardProps) {
  const { signOut } = useAuthContext()
  const [showWeightForm, setShowWeightForm] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)
  const [showSettings, setShowSettings] = useState(false)

  const handleLogout = async () => {
    try {
      // Sign out from Supabase
      await signOut()

      // Clear local storage
      SecureStorage.removeItem("user")
      localStorage.clear()
      sessionStorage.clear()

      // Redirect to login page
      window.location.href = "/"
    } catch (error) {
      console.error("Error during logout:", error)
      // Fallback: force clear everything and reload
      localStorage.clear()
      sessionStorage.clear()
      window.location.href = "/"
    }
  }

  const handleAddWeight = () => {
    setShowWeightForm(true)
  }

  const handleAddWorkout = () => {
    // TODO: Implement workout logging form
    console.log('Add workout clicked')
  }

  const handleWeightSuccess = () => {
    setRefreshKey(prev => prev + 1) // Trigger refresh of metrics
  }

  // Show settings page if requested
  if (showSettings) {
    return (
      <SettingsPage
        user={user}
        onBack={() => setShowSettings(false)}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="sticky top-0 z-50 border-b border-white/20 bg-white/80 backdrop-blur-xl">
        <div className="mx-auto flex h-16 lg:h-20 max-w-7xl items-center justify-between px-2 sm:px-4 lg:px-8">
          {/* Brand */}
          <div className="flex items-center gap-2 lg:gap-4">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-orange-500 to-red-500 lg:h-10 lg:w-10">
              <span className="text-xs font-bold text-white lg:text-sm">B&B</span>
            </div>
            <div>
              <h1 className="text-lg font-bold text-transparent bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text lg:text-2xl">
                Bars & Barbells Fit
              </h1>
              <p className="text-xs text-gray-500 hidden sm:block">Your fitness companion</p>
            </div>
          </div>

          {/* User Actions */}
          <div className="flex items-center gap-2 lg:gap-6">
            {/* User Avatar & Info */}
            <div className="flex items-center gap-2 lg:gap-3">
              <Avatar className="h-8 w-8 ring-2 ring-orange-200 lg:h-10 lg:w-10">
                <AvatarFallback className="bg-gradient-to-r from-orange-500 to-red-500 font-semibold text-white text-sm">
                  {user.firstName[0]}
                  {user.lastName[0]}
                </AvatarFallback>
              </Avatar>
              <div className="hidden sm:block">
                <p className="text-sm font-semibold text-gray-900">
                  {user.firstName} {user.lastName}
                </p>
                {user.program && <p className="text-xs text-gray-500">{user.program}</p>}
              </div>
            </div>

            {/* Settings Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(true)}
              className="text-gray-500 hover:text-gray-700 p-2"
            >
              <Settings className="h-4 w-4" />
            </Button>

            {/* Logout Buttons */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              className="hidden sm:flex items-center gap-2 border-gray-200 bg-transparent hover:bg-gray-50"
            >
              <LogOut className="h-4 w-4" />
              Logout
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              className="flex sm:hidden border-gray-200 bg-transparent hover:bg-gray-50 p-2"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="mx-auto max-w-7xl px-2 py-4 sm:px-4 lg:px-8 lg:py-8">
        {/* Welcome Section */}
        <section className="mb-6 lg:mb-8">
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 p-4 text-white lg:rounded-3xl lg:p-8">
            <div className="absolute inset-0 bg-black/10" />
            <div className="relative z-10">
              <h2 className="mb-2 text-2xl font-bold lg:text-4xl">Welcome back, {user.firstName}! 👋</h2>
              <p className="mb-4 text-base lg:text-lg text-white/90">Ready to crush your fitness goals today?</p>
              {user.program && (
                <div className="inline-flex items-center rounded-full bg-white/20 px-3 py-2 text-sm font-medium backdrop-blur-sm lg:px-4">
                  <span>Current Program: {user.program}</span>
                </div>
              )}
            </div>
            {/* Decorative Elements - Hidden on mobile for cleaner look */}
            <div className="absolute top-4 right-4 hidden h-16 w-16 animate-pulse-slow rounded-full bg-white/10 lg:block lg:h-32 lg:w-32" />
            <div className="absolute bottom-4 right-20 hidden h-10 w-10 animate-float rounded-full bg-white/10 lg:block lg:h-20 lg:w-20" />
          </div>
        </section>

        {/* Enhanced Navigation Tabs */}
        <Tabs defaultValue="dashboard" className="space-y-6 lg:space-y-8">
          <TabsList className="grid w-full grid-cols-4 bg-white/80 backdrop-blur-xl p-2 rounded-2xl shadow-lg border border-white/20 mx-auto max-w-3xl h-16">
            <TabsTrigger
              value="dashboard"
              className="flex items-center justify-center gap-2 h-full px-4 text-sm font-medium transition-all duration-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg rounded-xl"
              aria-label="Progress Dashboard"
            >
              <BarChart3 className="h-5 w-5" />
              <span>Progress</span>
            </TabsTrigger>
            <TabsTrigger
              value="workout"
              className="flex items-center justify-center gap-2 h-full px-4 text-sm font-medium transition-all duration-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg rounded-xl"
              aria-label="Workout Program"
            >
              <Dumbbell className="h-5 w-5" />
              <span>Workout</span>
            </TabsTrigger>
            <TabsTrigger
              value="nutrition"
              className="flex items-center justify-center gap-2 h-full px-4 text-sm font-medium transition-all duration-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg rounded-xl"
              aria-label="Nutrition Guide"
            >
              <Apple className="h-5 w-5" />
              <span>Nutrition</span>
            </TabsTrigger>
            <TabsTrigger
              value="recommendations"
              className="flex items-center justify-center gap-2 h-full px-4 text-sm font-medium transition-all duration-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg rounded-xl"
              aria-label="Recommendations"
            >
              <ShoppingCart className="h-5 w-5" />
              <span>Recommendations</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            <MetricsOverview
              key={refreshKey}
              onAddWorkout={handleAddWorkout}
            />

            {/* Progress Charts and Photos */}
            <ProgressCheckinDashboard user={user} />
          </TabsContent>

          <TabsContent value="workout" className="space-y-6">
            <WorkoutProgram program={user.program ?? ""} />
          </TabsContent>

          <TabsContent value="nutrition" className="space-y-6">
            <NutritionGuide />
          </TabsContent>

          <TabsContent value="recommendations" className="space-y-6">
            <Recommendations />
          </TabsContent>
        </Tabs>
      </main>

      {/* Weight Entry Form Modal */}
      {showWeightForm && (
        <WeightEntryForm
          onClose={() => setShowWeightForm(false)}
          onSuccess={handleWeightSuccess}
        />
      )}
    </div>
  )
}

// Export both named and default to satisfy all import styles
export default Dashboard
