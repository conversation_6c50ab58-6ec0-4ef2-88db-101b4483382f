"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { MessageSquare, TrendingUp, Heart, Zap, Smile, Meh, Frown, CheckCircle } from "lucide-react"
import { CheckInFeedbackProps, WorkoutDifficulty } from "@/lib/types"

export function CheckInFeedback({ user }: CheckInFeedbackProps) {
  const [energyLevel, setEnergyLevel] = useState([7])
  const [motivation, setMotivation] = useState([8])
  const [workoutDifficulty, setWorkoutDifficulty] = useState<WorkoutDifficulty | "">("")
  const [feedback, setFeedback] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    alert("Check-in submitted successfully! 🎉")
  }

  const recentCheckIns = [
    {
      date: "2024-02-10",
      energy: 8,
      motivation: 9,
      mood: "great",
      notes: "Great workout today! Feeling strong and motivated. Hit a new PR!",
    },
    {
      date: "2024-02-08",
      energy: 6,
      motivation: 7,
      mood: "okay",
      notes: "Bit tired but pushed through. Need more sleep but still completed the workout.",
    },
    {
      date: "2024-02-06",
      energy: 9,
      motivation: 8,
      mood: "great",
      notes: "Amazing session! Hit a new PR on bench press. Feeling unstoppable!",
    },
  ]

  const getMoodIcon = (mood: string) => {
    switch (mood) {
      case "great":
        return <Smile className="h-4 w-4 text-green-600" />
      case "okay":
        return <Meh className="h-4 w-4 text-yellow-600" />
      case "poor":
        return <Frown className="h-4 w-4 text-red-600" />
      default:
        return <Meh className="h-4 w-4 text-gray-600" />
    }
  }

  const getEnergyColor = (level: number) => {
    if (level >= 8) return "text-green-600"
    if (level >= 6) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <Card className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white border-0 shadow-2xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-3xl font-bold mb-2 flex items-center">
                <MessageSquare className="h-8 w-8 mr-3" />
                Weekly Check-in
              </CardTitle>
              <CardDescription className="text-white/90 text-lg">
                Share your progress and help us optimize your program
              </CardDescription>
            </div>
            <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm">
              <Heart className="h-8 w-8 text-white" />
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Check-in Form */}
      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-gray-900">How are you feeling this week?</CardTitle>
          <CardDescription>Your feedback helps us personalize your fitness journey</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Energy Level */}
            <div className="space-y-4">
              <Label className="text-lg font-semibold text-gray-900">Energy Level</Label>
              <div className="px-4">
                <Slider
                  value={energyLevel}
                  onValueChange={setEnergyLevel}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-2">
                  <span>Exhausted</span>
                  <span className={`font-bold text-lg ${getEnergyColor(energyLevel[0])}`}>{energyLevel[0]}/10</span>
                  <span>Energized</span>
                </div>
              </div>
            </div>

            {/* Motivation Level */}
            <div className="space-y-4">
              <Label className="text-lg font-semibold text-gray-900">Motivation Level</Label>
              <div className="px-4">
                <Slider value={motivation} onValueChange={setMotivation} max={10} min={1} step={1} className="w-full" />
                <div className="flex justify-between text-sm text-gray-500 mt-2">
                  <span>Not motivated</span>
                  <span className={`font-bold text-lg ${getEnergyColor(motivation[0])}`}>{motivation[0]}/10</span>
                  <span>Highly motivated</span>
                </div>
              </div>
            </div>

            {/* Workout Difficulty */}
            <div className="space-y-4">
              <Label className="text-lg font-semibold text-gray-900">How did your workouts feel this week?</Label>
              <RadioGroup value={workoutDifficulty} onValueChange={setWorkoutDifficulty}>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="relative">
                    <RadioGroupItem value="too-easy" id="too-easy" className="peer sr-only" />
                    <Label
                      htmlFor="too-easy"
                      className="flex flex-col items-center p-6 bg-gradient-to-br from-green-50 to-emerald-100 border-2 border-green-200 rounded-xl cursor-pointer transition-all duration-300 hover:shadow-md peer-checked:border-green-500 peer-checked:bg-green-100"
                    >
                      <Smile className="h-8 w-8 text-green-600 mb-3" />
                      <span className="font-semibold text-green-800">Too Easy</span>
                      <span className="text-sm text-green-600 text-center mt-1">I could do more</span>
                    </Label>
                  </div>

                  <div className="relative">
                    <RadioGroupItem value="just-right" id="just-right" className="peer sr-only" />
                    <Label
                      htmlFor="just-right"
                      className="flex flex-col items-center p-6 bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-xl cursor-pointer transition-all duration-300 hover:shadow-md peer-checked:border-blue-500 peer-checked:bg-blue-100"
                    >
                      <CheckCircle className="h-8 w-8 text-blue-600 mb-3" />
                      <span className="font-semibold text-blue-800">Just Right</span>
                      <span className="text-sm text-blue-600 text-center mt-1">Challenging but manageable</span>
                    </Label>
                  </div>

                  <div className="relative">
                    <RadioGroupItem value="too-hard" id="too-hard" className="peer sr-only" />
                    <Label
                      htmlFor="too-hard"
                      className="flex flex-col items-center p-6 bg-gradient-to-br from-red-50 to-pink-100 border-2 border-red-200 rounded-xl cursor-pointer transition-all duration-300 hover:shadow-md peer-checked:border-red-500 peer-checked:bg-red-100"
                    >
                      <Frown className="h-8 w-8 text-red-600 mb-3" />
                      <span className="font-semibold text-red-800">Too Hard</span>
                      <span className="text-sm text-red-600 text-center mt-1">Struggling to complete</span>
                    </Label>
                  </div>
                </div>
              </RadioGroup>
            </div>

            {/* Additional Feedback */}
            <div className="space-y-4">
              <Label htmlFor="feedback" className="text-lg font-semibold text-gray-900">
                Additional thoughts or concerns
              </Label>
              <Textarea
                id="feedback"
                placeholder="Share any thoughts, achievements, challenges, or questions from this week..."
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                rows={4}
                className="bg-white/50 border-gray-200 focus:border-purple-500 focus:ring-purple-500"
              />
            </div>

            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-4 rounded-xl transition-all duration-300 transform hover:scale-105"
            >
              Submit Weekly Check-in
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Recent Check-ins */}
      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
        <CardHeader>
          <CardTitle className="flex items-center text-2xl font-bold text-gray-900">
            <TrendingUp className="h-6 w-6 mr-3 text-blue-600" />
            Recent Check-ins
          </CardTitle>
          <CardDescription>Your progress and feedback over the past few weeks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {recentCheckIns.map((checkin, index) => (
              <div
                key={index}
                className="p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl border border-gray-200 hover:shadow-md transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-500 rounded-lg">{getMoodIcon(checkin.mood)}</div>
                    <span className="font-semibold text-gray-900 text-lg">
                      {new Date(checkin.date).toLocaleDateString("en-US", {
                        weekday: "long",
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </span>
                  </div>
                  <div className="flex space-x-3">
                    <Badge variant="outline" className="flex items-center bg-blue-50 border-blue-200">
                      <Zap className="h-3 w-3 mr-1 text-blue-600" />
                      Energy: {checkin.energy}/10
                    </Badge>
                    <Badge variant="outline" className="flex items-center bg-purple-50 border-purple-200">
                      <Heart className="h-3 w-3 mr-1 text-purple-600" />
                      Motivation: {checkin.motivation}/10
                    </Badge>
                  </div>
                </div>
                <p className="text-gray-700 leading-relaxed bg-white/50 p-4 rounded-xl">{checkin.notes}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
