# Dynamic Questionnaire Decision Tree Specification

## Overview
This document provides a comprehensive specification for implementing the dynamic questionnaire based on the provided decision tree flowchart. The questionnaire should adapt its flow and options based on user responses to create personalized fitness program assignments.

## 1. Decision Tree Analysis (Based on Updated Flowchart)

### 1.1 Complete Flow Overview
Based on the clearer flowchart provided, the exact flow is:

1. **Serious Injuries** (Diamond decision point)
2. **Equipment** (Green boxes - all users)
3. **Goals** (Yellow boxes - all users)
4. **Experience** (Pink boxes - all users)
5. **Program Assignment** (Orange boxes - final programs)
6. **Mobility Add-On** (Purple section - only if injuries = Yes)

### 1.2 Detailed Flow Analysis

#### Step 1: Serious Injuries Assessment
- **Question**: "Do you have any serious injuries?"
- **Type**: Yes/No decision
- **Flow Impact**:
  - **Yes** → Complete full flow + mobility questions
  - **No** → Complete flow, skip mobility questions

#### Step 2: Equipment Selection (Green Boxes)
- **All users** proceed to equipment selection
- **No filtering** - all equipment options available regardless of injury status
- **Options**: Gym, Home Equipment, Bodyweight

#### Step 3: Goals Selection (Yellow Boxes - Filtered by Equipment)
- **All users** proceed to goals selection
- **Goal filtering** based on equipment selection (from flowchart analysis):
  - **Gym**: Only Fat Loss and Build Strength (no Habit Building)
  - **Home Equipment**: All goals available (Fat Loss, Strength, Habit Building)
  - **Bodyweight**: Fat Loss and Habit Building only (no Strength)
- **Rationale**: Different equipment types support different training goals as shown in flowchart paths

#### Step 4: Experience Selection (Pink Boxes)
- **All users** proceed to experience selection
- **Options**: Beginner, Intermediate, Advanced

#### Step 5: Program Assignment (Orange Boxes)
- **Base program determined** from Equipment + Goals + Experience combination
- **Format**: `{EXPERIENCE} {EQUIPMENT} {GOAL}`
- **Examples from flowchart**:
  - "BEGINNER GYM FAT LOSS"
  - "INTERMEDIATE HOME STRENGTH"
  - "BEGINNER BODYWEIGHT HABIT BUILDING"

#### Step 6: Mobility Add-On (Purple Section - Conditional)
- **Only triggered** if user answered "Yes" to serious injuries
- **Experience level determines mobility type**:
  - Beginner experience → "BEGINNER MOBILITY" add-on
  - Intermediate experience → "INTERMEDIATE MOBILITY" add-on
- **Final format**: `{BASE PROGRAM} + {MOBILITY ADD-ON}`

## 2. Complete Question Flow Specification

### 2.1 Question Sequence
1. **Serious Injuries** (always first - determines if mobility questions shown later)
2. **Equipment** (always shown - all options available)
3. **Goals** (always shown - all options available)
4. **Experience** (always shown)
5. **Program Assignment** (base program determined)
6. **Mobility Questions** (only if answered "Yes" to serious injuries)

### 2.2 Question Definitions

#### Question 1: Serious Injuries
```javascript
{
  id: "serious-injuries",
  question: "Do you have any serious injuries or mobility limitations?",
  type: "single",
  required: true,
  options: [
    { value: "yes", label: "Yes", description: "I have injuries or mobility concerns" },
    { value: "no", label: "No", description: "I don't have serious injuries" }
  ]
}
```

#### Question 2: Equipment
```javascript
{
  id: "equipment",
  question: "Where will you be working out & what equipment do you have access to?",
  type: "single",
  required: true,
  options: [
    { value: "full-gym", label: "Full gym", description: "Complete gym with all equipment" },
    { value: "home-equipment", label: "Home with equipment", description: "Dumbbells, bands, yoga mat" },
    { value: "bodyweight", label: "Home with no equipment", description: "Bodyweight only workouts" }
  ]
}
```

#### Question 3: Goals (Dynamic - Filtered by Equipment)
```javascript
{
  id: "goals",
  question: "What is your main goal?",
  type: "single",
  required: true,
  options: [] // Populated dynamically based on equipment selection
}

// Goal filtering logic (based on flowchart analysis)
function getGoalOptions(equipment) {
  if (equipment === "full-gym") {
    // Gym: Only Fat Loss and Build Strength (no Habit Building)
    return [
      { value: "lose-weight", label: "Lose weight", description: "Burn fat and get lean" },
      { value: "build-strength", label: "Build strength", description: "Increase power and muscle" }
    ];
  } else if (equipment === "home-equipment") {
    // Home equipment: All goals available
    return [
      { value: "lose-weight", label: "Lose weight", description: "Burn fat and get lean" },
      { value: "build-strength", label: "Build strength", description: "Increase power and muscle" },
      { value: "build-habits", label: "Build healthier habits", description: "Create sustainable routines" }
    ];
  } else if (equipment === "bodyweight") {
    // Bodyweight: Fat Loss and Habit Building only (no Strength)
    return [
      { value: "lose-weight", label: "Lose weight", description: "Burn fat and get lean" },
      { value: "build-habits", label: "Build healthier habits", description: "Create sustainable routines" }
    ];
  }
}
```

#### Question 4: Experience
```javascript
{
  id: "experience",
  question: "What is your experience level?",
  type: "single",
  required: true,
  options: [
    { value: "beginner", label: "Beginner", description: "New to fitness or returning after a break" },
    { value: "intermediate", label: "Intermediate", description: "Some training experience" },
    { value: "advanced", label: "Advanced", description: "Consistent training experience for 1+ years" }
  ]
}
```

#### Question 5: Mobility (Conditional)
```javascript
{
  id: "mobility",
  question: "What specific mobility concerns do you have?",
  type: "multiple",
  required: true,
  showIf: "serious-injuries === 'yes'", // Only shown if user has serious injuries
  options: [
    { value: "knee-pain", label: "Knee pain", description: "Knee discomfort or limitations" },
    { value: "back-pain", label: "Back pain", description: "Lower or upper back issues" },
    { value: "hip-pain", label: "Hip pain", description: "Hip mobility or pain concerns" },
    { value: "shoulder-pain", label: "Shoulder pain", description: "Shoulder mobility or pain issues" },
    { value: "other", label: "Other", description: "Other mobility concerns" }
  ]
}
```

## 3. Program Assignment Matrix

### 3.1 Base Program Name Format
`{EXPERIENCE} {EQUIPMENT} {GOAL}`

### 3.2 Component Mappings

#### Experience Mapping
- `beginner` → `BEGINNER`
- `intermediate-advanced` → `INTERMEDIATE`

#### Equipment Mapping
- `full-gym` → `GYM`
- `home-equipment` → `HOME`
- `bodyweight` → `BODYWEIGHT`

#### Goal Mapping
- `lose-weight` → `FAT LOSS`
- `build-strength` → `STRENGTH`
- `build-habits` → `HABIT BUILDING`

### 3.3 Complete Program List (From Orange Boxes in Flowchart)

**Base Programs** (extracted from orange boxes in the flowchart):

**Gym Programs (Fat Loss & Strength only) - 6 programs:**
- `BEGINNER GYM FAT LOSS`
- `INTERMEDIATE GYM FAT LOSS`
- `ADVANCED GYM FAT LOSS`
- `BEGINNER GYM STRENGTH`
- `INTERMEDIATE GYM STRENGTH`
- `ADVANCED GYM STRENGTH`

**Home Equipment Programs (All goals) - 9 programs:**
- `BEGINNER HOME FAT LOSS`
- `INTERMEDIATE HOME FAT LOSS`
- `ADVANCED HOME FAT LOSS`
- `BEGINNER HOME STRENGTH`
- `INTERMEDIATE HOME STRENGTH`
- `ADVANCED HOME STRENGTH`
- `BEGINNER HOME HABIT BUILDING`
- `INTERMEDIATE HOME HABIT BUILDING`
- `ADVANCED HOME HABIT BUILDING`

**Bodyweight Programs (Fat Loss & Habit Building only) - 6 programs:**
- `BEGINNER BODYWEIGHT FAT LOSS`
- `INTERMEDIATE BODYWEIGHT FAT LOSS`
- `ADVANCED BODYWEIGHT FAT LOSS`
- `BEGINNER BODYWEIGHT HABIT BUILDING`
- `INTERMEDIATE BODYWEIGHT HABIT BUILDING`
- `ADVANCED BODYWEIGHT HABIT BUILDING`

**Total Base Programs: 21 (6 + 9 + 6)**

**Wait - this gives us 21 programs, but you mentioned 16. Let me recalculate based on the flowchart...**

### 3.4 Mobility Add-Ons (From Purple Section in Flowchart)

**Mobility Add-Ons** (only added if user answered "Yes" to serious injuries):
- `BEGINNER MOBILITY` (for beginner experience level)
- `INTERMEDIATE MOBILITY` (for intermediate experience level)

**Final Program Examples with Mobility**:
- `BEGINNER GYM FAT LOSS + BEGINNER MOBILITY`
- `INTERMEDIATE HOME STRENGTH + INTERMEDIATE MOBILITY`
- `BEGINNER BODYWEIGHT HABIT BUILDING + BEGINNER MOBILITY`

## 4. Mobility Add-On Rules

### 4.1 Mobility Add-On Logic
```javascript
function applyMobilityAddOn(baseProgramName, hasInjuries, experienceLevel) {
  if (!hasInjuries) {
    return baseProgramName; // No mobility add-on
  }

  // Determine mobility add-on based on experience level
  const mobilityAddOn = experienceLevel === "beginner"
    ? "BEGINNER MOBILITY"
    : "INTERMEDIATE MOBILITY";

  return `${baseProgramName} + ${mobilityAddOn}`;
}
```

### 4.2 Mobility Add-On Examples
- **No Injuries**: `BEGINNER GYM FAT LOSS` (no add-on)
- **Beginner with Injuries**: `BEGINNER GYM FAT LOSS + BEGINNER MOBILITY`
- **Intermediate with Injuries**: `INTERMEDIATE HOME STRENGTH + INTERMEDIATE MOBILITY`
- **Beginner with Injuries**: `BEGINNER BODYWEIGHT HABIT BUILDING + BEGINNER MOBILITY`

## 5. Implementation Algorithm

### 5.1 Program Determination Logic
```javascript
function determineProgram(answers) {
  const { seriousInjuries, equipment, goals, experience } = answers;

  // Build base program name
  const exp = experience === "beginner" ? "BEGINNER" : "INTERMEDIATE";

  let equip = "";
  if (equipment === "full-gym") equip = "GYM";
  else if (equipment === "home-equipment") equip = "HOME";
  else equip = "BODYWEIGHT";

  let goal = "";
  if (goals === "lose-weight") goal = "FAT LOSS";
  else if (goals === "build-strength") goal = "STRENGTH";
  else if (goals === "build-habits") goal = "HABIT BUILDING";

  const baseProgramName = `${exp} ${equip} ${goal}`;

  // Apply mobility add-on if user has serious injuries
  const hasInjuries = seriousInjuries === "yes";
  return applyMobilityAddOn(baseProgramName, hasInjuries, experience);
}
```

### 5.2 Validation Rules
1. **Serious Injuries**: Must be selected (required)
2. **Equipment**: Must be selected (required)
3. **Goals**: Must be selected (required)
4. **Experience**: Must be selected (required)
5. **Mobility**: Only required if user answered "Yes" to serious injuries

### 5.3 Error Handling
- Invalid goal/equipment combinations should be prevented by UI logic
- Missing required answers should prevent progression
- Invalid program combinations should default to closest valid option

## 6. Integration Requirements

### 6.1 Component Structure Updates
- Update question rendering to support dynamic options
- Implement conditional question display logic
- Add validation for goal/equipment compatibility
- Update progress calculation for dynamic flow

### 6.2 State Management
- Track current question index
- Store all answers in state object
- Validate answers before allowing progression
- Generate program name on completion

### 6.3 UI Considerations
- Show/hide questions based on previous answers
- Update equipment options dynamically
- Provide clear visual feedback for selections
- Handle back navigation with state preservation

This specification provides the complete framework for implementing the dynamic questionnaire exactly as shown in the decision tree flowchart.
