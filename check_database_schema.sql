-- Run this in your Supabase SQL Editor to check current schema
-- This will show you exactly what fields are currently in your intake_responses table

-- Check current table structure
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default,
  ordinal_position
FROM information_schema.columns 
WHERE table_name = 'intake_responses' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- Also check if there are any records and what fields they contain
SELECT 
  COUNT(*) as total_records,
  COUNT(CASE WHEN has_limitations IS NOT NULL THEN 1 END) as has_limitations_count,
  COUNT(CASE WHEN location IS NOT NULL THEN 1 END) as location_count,
  COUNT(CASE WHEN goal IS NOT NULL THEN 1 END) as goal_count,
  COUNT(CASE WHEN experience IS NOT NULL THEN 1 END) as experience_count,
  COUNT(CASE WHEN wants_mobility IS NOT NULL THEN 1 END) as wants_mobility_count,
  COUNT(CASE WHEN program_id IS NOT NULL THEN 1 END) as program_id_count,
  COUNT(CASE WHEN program_name IS NOT NULL THEN 1 END) as program_name_count
FROM intake_responses;

-- Check if old fields still exist (this will error if they don't exist - which is good)
-- Comment out any lines that cause errors - those fields have been successfully removed
SELECT 
  COUNT(CASE WHEN goals IS NOT NULL THEN 1 END) as old_goals_count,
  COUNT(CASE WHEN equipment IS NOT NULL THEN 1 END) as old_equipment_count,
  COUNT(CASE WHEN frequency IS NOT NULL THEN 1 END) as old_frequency_count,
  COUNT(CASE WHEN mobility_concerns IS NOT NULL THEN 1 END) as old_mobility_concerns_count
FROM intake_responses;

-- Show a sample record to see the actual data structure
SELECT * FROM intake_responses LIMIT 1;
