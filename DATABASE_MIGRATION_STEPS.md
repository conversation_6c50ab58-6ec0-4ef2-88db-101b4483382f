# Database Schema Migration - Step-by-Step Process

## Current Issue Analysis
Your `intake_responses` table still contains old fields like "frequency" because the migration hasn't been properly executed.

## Pre-Migration Verification

### Step 1: Check Current Table Structure
Run this in Supabase SQL Editor to see current schema:
```sql
-- Check current table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'intake_responses' 
ORDER BY ordinal_position;
```

### Step 2: Backup Existing Data (CRITICAL)
```sql
-- Create backup table with current data
CREATE TABLE intake_responses_backup_$(date +%Y%m%d) AS 
SELECT * FROM intake_responses;

-- Verify backup was created
SELECT COUNT(*) FROM intake_responses_backup_$(date +%Y%m%d);
```

## Migration Execution

### Step 3: Run Complete Migration
Copy and paste this ENTIRE script in Supabase SQL Editor:

```sql
-- Migration to update intake_responses table for new questionnaire structure
-- IMPORTANT: This will DROP all existing data in intake_responses table

-- Drop the existing table and recreate with new structure
DROP TABLE IF EXISTS intake_responses;

-- Create the new intake_responses table with updated schema
CREATE TABLE intake_responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  has_limitations BOOLEAN NOT NULL DEFAULT false,
  location TEXT NOT NULL CHECK (location IN ('home_no_equipment', 'home_willing_to_purchase', 'home_with_equipment', 'gym')),
  goal TEXT NOT NULL CHECK (goal IN ('fat_loss', 'build_strength', 'healthy_habits')),
  experience TEXT NOT NULL CHECK (experience IN ('beginner', 'intermediate', 'advanced')),
  wants_mobility BOOLEAN NOT NULL DEFAULT false,
  flex_score INTEGER CHECK (flex_score >= 1 AND flex_score <= 10),
  mobility_add_on TEXT NOT NULL DEFAULT 'none' CHECK (mobility_add_on IN ('none', 'beginner', 'advanced')),
  program_id TEXT NOT NULL,
  program_name TEXT NOT NULL,
  completion_status TEXT NOT NULL DEFAULT 'complete' CHECK (completion_status IN ('complete', 'incomplete')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_intake_responses_user_id ON intake_responses(user_id);
CREATE INDEX idx_intake_responses_completion_status ON intake_responses(completion_status);
CREATE INDEX idx_intake_responses_created_at ON intake_responses(created_at);

-- Enable Row Level Security
ALTER TABLE intake_responses ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own intake responses" ON intake_responses
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own intake responses" ON intake_responses
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own intake responses" ON intake_responses
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own intake responses" ON intake_responses
  FOR DELETE USING (auth.uid() = user_id);

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_intake_responses_updated_at
  BEFORE UPDATE ON intake_responses
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT ALL ON intake_responses TO authenticated;
GRANT ALL ON intake_responses TO service_role;
```

### Step 4: Verify Migration Success
```sql
-- Verify new table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'intake_responses' 
ORDER BY ordinal_position;

-- Should show ONLY these fields:
-- id, user_id, has_limitations, location, goal, experience, 
-- wants_mobility, flex_score, mobility_add_on, program_id, 
-- program_name, completion_status, created_at, updated_at
```

### Step 5: Test with Sample Data
```sql
-- Insert test record to verify schema works
INSERT INTO intake_responses (
  user_id,
  has_limitations,
  location,
  goal,
  experience,
  wants_mobility,
  flex_score,
  mobility_add_on,
  program_id,
  program_name
) VALUES (
  auth.uid(),
  false,
  'gym',
  'fat_loss',
  'beginner',
  true,
  5,
  'beginner',
  'FL_GYM_BEG',
  'Fat Loss • Gym • Beginner'
);

-- Verify insert worked
SELECT * FROM intake_responses WHERE user_id = auth.uid();
```

## Post-Migration Verification

### Expected Final Schema:
- ✅ `id` (UUID, Primary Key)
- ✅ `user_id` (UUID, Foreign Key)
- ✅ `has_limitations` (BOOLEAN)
- ✅ `location` (TEXT with constraints)
- ✅ `goal` (TEXT with constraints)
- ✅ `experience` (TEXT with constraints)
- ✅ `wants_mobility` (BOOLEAN)
- ✅ `flex_score` (INTEGER, nullable)
- ✅ `mobility_add_on` (TEXT with constraints)
- ✅ `program_id` (TEXT)
- ✅ `program_name` (TEXT)
- ✅ `completion_status` (TEXT with constraints)
- ✅ `created_at` (TIMESTAMP)
- ✅ `updated_at` (TIMESTAMP)

### Fields That Should Be REMOVED:
- ❌ `goals` (old array field)
- ❌ `equipment` (renamed to `location`)
- ❌ `frequency` (removed entirely)
- ❌ `mobility_concerns` (replaced with `wants_mobility` + `flex_score`)

## Troubleshooting

If migration fails:
1. **Check for foreign key constraints** - Users must exist in auth.users
2. **Verify RLS policies** - May need to temporarily disable RLS
3. **Check permissions** - Ensure you have admin access
4. **Review error messages** - Supabase will show specific constraint violations

## Rollback Process (if needed)
```sql
-- If you need to rollback
DROP TABLE intake_responses;
ALTER TABLE intake_responses_backup_YYYYMMDD RENAME TO intake_responses;
```
