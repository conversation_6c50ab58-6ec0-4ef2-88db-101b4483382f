import { AppError } from './types'

export class AppErrorHandler {
  static createError(message: string, type: AppError['type'], field?: string): AppError {
    return {
      message,
      type,
      field
    }
  }

  static handleStorageError(error: unknown): AppError {
    if (error instanceof Error) {
      return this.createError(
        'Unable to save your data. Please try again.',
        'storage'
      )
    }
    return this.createError(
      'An unexpected error occurred while saving your data.',
      'storage'
    )
  }

  static handleValidationError(field: string, message: string): AppError {
    return this.createError(message, 'validation', field)
  }

  static handleNetworkError(error: unknown): AppError {
    return this.createError(
      'Network error. Please check your connection and try again.',
      'network'
    )
  }

  static handleGeneralError(error: unknown): AppError {
    if (error instanceof Error) {
      return this.createError(error.message, 'general')
    }
    return this.createError(
      'An unexpected error occurred. Please try again.',
      'general'
    )
  }

  static getErrorMessage(error: AppError): string {
    return error.message
  }

  static isValidationError(error: AppError): boolean {
    return error.type === 'validation'
  }

  static isStorageError(error: AppError): boolean {
    return error.type === 'storage'
  }
}
