"use client"

import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { AppState, User, CurrentStep, ErrorState, ProgressData, WorkoutData } from './types'
import { SecureStorage } from './secure-storage'
import { AppErrorHandler } from './error-handler'
import { DataService } from './data-service'

// Initial state
const initialState: AppState = {
  user: null,
  currentStep: 'signup',
  errorState: { hasError: false, error: null },
  progressData: {
    weight: [],
    workouts: []
  },
  workoutData: {
    currentProgram: null,
    completedWorkouts: [],
    currentWorkout: null,
    workoutHistory: []
  }
}

// Action types
type AppAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_CURRENT_STEP'; payload: CurrentStep }
  | { type: 'SET_ERROR'; payload: ErrorState }
  | { type: 'CLEAR_ERROR' }
  | { type: 'SET_PROGRESS_DATA'; payload: Partial<ProgressData> }
  | { type: 'SET_WORKOUT_DATA'; payload: Partial<WorkoutData> }
  | { type: 'ADD_WEIGHT_DATA'; payload: { date: string; value: number } }
  | { type: 'ADD_WORKOUT_PROGRESS'; payload: { date: string; workouts: number; target: number } }
  | { type: 'ADD_BODY_MEASUREMENT'; payload: { type: string; date: string; value: number } }
  | { type: 'ADD_CHECK_IN'; payload: any }
  | { type: 'LOAD_STATE'; payload: Partial<AppState> }
  | { type: 'RESET_STATE' }

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload }
    
    case 'SET_CURRENT_STEP':
      return { ...state, currentStep: action.payload }
    
    case 'SET_ERROR':
      return { ...state, errorState: action.payload }
    
    case 'CLEAR_ERROR':
      return { ...state, errorState: { hasError: false, error: null } }
    
    case 'SET_PROGRESS_DATA':
      return {
        ...state,
        progressData: { ...state.progressData, ...action.payload }
      }
    
    case 'SET_WORKOUT_DATA':
      return {
        ...state,
        workoutData: { ...state.workoutData, ...action.payload }
      }
    
    case 'ADD_WEIGHT_DATA':
      return {
        ...state,
        progressData: {
          ...state.progressData,
          weight: [...state.progressData.weight, action.payload]
        }
      }
    
    case 'ADD_WORKOUT_PROGRESS':
      return {
        ...state,
        progressData: {
          ...state.progressData,
          workouts: [...state.progressData.workouts, action.payload]
        }
      }


    
    case 'LOAD_STATE':
      return { ...state, ...action.payload }
    
    case 'RESET_STATE':
      return initialState
    
    default:
      return state
  }
}

// Context
const AppContext = createContext<{
  state: AppState
  dispatch: React.Dispatch<AppAction>
} | null>(null)

// Provider component
export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState)

  // Load state from secure storage on mount
  useEffect(() => {
    try {
      if (SecureStorage.isAvailable()) {
        // Clear old user data since we're now using Supabase auth
        SecureStorage.removeItem('user')

        const savedProgressData = SecureStorage.getItem<ProgressData>('progressData')
        const savedWorkoutData = SecureStorage.getItem<WorkoutData>('workoutData')

        // Don't auto-load user data anymore - let Supabase auth handle this
        // The main page will set the user and step based on Supabase auth state

        if (savedProgressData) {
          dispatch({ type: 'SET_PROGRESS_DATA', payload: savedProgressData })
        } else {
          // Initialize with mock data for demonstration
          const initialProgressData = DataService.getInitialProgressData()
          dispatch({ type: 'SET_PROGRESS_DATA', payload: initialProgressData })
        }

        if (savedWorkoutData) {
          dispatch({ type: 'SET_WORKOUT_DATA', payload: savedWorkoutData })
        } else {
          // Initialize with empty workout data
          const initialWorkoutData = DataService.getInitialWorkoutData()
          dispatch({ type: 'SET_WORKOUT_DATA', payload: initialWorkoutData })
        }
      }
    } catch (error) {
      const appError = AppErrorHandler.handleStorageError(error)
      dispatch({ type: 'SET_ERROR', payload: { hasError: true, error: appError } })
    }
  }, [])

  // Save state to secure storage when it changes
  useEffect(() => {
    try {
      // Don't save user data to local storage anymore - Supabase handles auth persistence
      if (state.progressData) {
        SecureStorage.setItem('progressData', state.progressData)
      }
      if (state.workoutData) {
        SecureStorage.setItem('workoutData', state.workoutData)
      }
    } catch (error) {
      const appError = AppErrorHandler.handleStorageError(error)
      dispatch({ type: 'SET_ERROR', payload: { hasError: true, error: appError } })
    }
  }, [state.user, state.progressData, state.workoutData])

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  )
}

// Hook to use the context
export function useAppContext() {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider')
  }
  return context
}

// Helper hooks for specific data
export function useUser() {
  const { state, dispatch } = useAppContext()
  return {
    user: state.user,
    setUser: (user: User | null) => dispatch({ type: 'SET_USER', payload: user }),
    currentStep: state.currentStep,
    setCurrentStep: (step: CurrentStep) => dispatch({ type: 'SET_CURRENT_STEP', payload: step })
  }
}

export function useProgress() {
  const { state, dispatch } = useAppContext()
  return {
    progressData: state.progressData,
    addWeightData: (date: string, value: number) =>
      dispatch({ type: 'ADD_WEIGHT_DATA', payload: { date, value } }),
    addWorkoutProgress: (date: string, workouts: number, target: number) =>
      dispatch({ type: 'ADD_WORKOUT_PROGRESS', payload: { date, workouts, target } })
  }
}

export function useError() {
  const { state, dispatch } = useAppContext()
  return {
    errorState: state.errorState,
    setError: (error: ErrorState) => dispatch({ type: 'SET_ERROR', payload: error }),
    clearError: () => dispatch({ type: 'CLEAR_ERROR' })
  }
}
