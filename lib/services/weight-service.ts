import { supabase } from '../supabase'

export interface WeightEntry {
  id?: string
  user_id: string
  weight_value: number
  unit: 'lbs' | 'kg'
  recorded_date: string
  notes?: string
  created_at?: string
  updated_at?: string
}

export class WeightService {
  /**
   * Get all weight entries for a user
   */
  static async getUserWeightEntries(userId: string): Promise<{ data: WeightEntry[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('weight_entries')
        .select('*')
        .eq('user_id', userId)
        .order('recorded_date', { ascending: false })

      return { data, error }
    } catch (error) {
      console.error('Error fetching weight entries:', error)
      return { data: null, error }
    }
  }

  /**
   * Add a new weight entry
   */
  static async addWeightEntry(entry: Omit<WeightEntry, 'id' | 'created_at' | 'updated_at'>): Promise<{ data: WeightEntry | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('weight_entries')
        .insert(entry)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error adding weight entry:', error)
      return { data: null, error }
    }
  }

  /**
   * Update a weight entry
   */
  static async updateWeightEntry(id: string, updates: Partial<WeightEntry>): Promise<{ data: WeightEntry | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('weight_entries')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error updating weight entry:', error)
      return { data: null, error }
    }
  }

  /**
   * Delete a weight entry
   */
  static async deleteWeightEntry(id: string): Promise<{ error: any }> {
    try {
      const { error } = await supabase
        .from('weight_entries')
        .delete()
        .eq('id', id)

      return { error }
    } catch (error) {
      console.error('Error deleting weight entry:', error)
      return { error }
    }
  }

  /**
   * Get latest weight entry for a user
   */
  static async getLatestWeight(userId: string): Promise<{ data: WeightEntry | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('weight_entries')
        .select('*')
        .eq('user_id', userId)
        .order('recorded_date', { ascending: false })
        .limit(1)
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error fetching latest weight:', error)
      return { data: null, error }
    }
  }

  /**
   * Get weight entries for a date range
   */
  static async getWeightEntriesInRange(
    userId: string, 
    startDate: string, 
    endDate: string
  ): Promise<{ data: WeightEntry[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('weight_entries')
        .select('*')
        .eq('user_id', userId)
        .gte('recorded_date', startDate)
        .lte('recorded_date', endDate)
        .order('recorded_date', { ascending: true })

      return { data, error }
    } catch (error) {
      console.error('Error fetching weight entries in range:', error)
      return { data: null, error }
    }
  }

  /**
   * Calculate weight change over time
   */
  static async getWeightProgress(userId: string, days: number = 30): Promise<{
    data: {
      startWeight: number | null;
      currentWeight: number | null;
      change: number | null;
      changePercentage: number | null;
      totalChange: number | null;
    } | null;
    error: any
  }> {
    try {
      const endDate = new Date().toISOString().split('T')[0]
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

      const { data: entries, error } = await this.getWeightEntriesInRange(userId, startDate, endDate)

      if (error || !entries || entries.length === 0) {
        // If no entries in range, get all entries to calculate total change
        const { data: allEntries } = await this.getUserWeightEntries(userId)
        if (allEntries && allEntries.length > 0) {
          // Sort by date ascending to get first and last entries
          const sortedEntries = allEntries.sort((a, b) =>
            new Date(a.recorded_date).getTime() - new Date(b.recorded_date).getTime()
          )
          const firstWeight = sortedEntries[0].weight_value
          const lastWeight = sortedEntries[sortedEntries.length - 1].weight_value
          const totalChange = lastWeight - firstWeight

          return {
            data: {
              startWeight: null,
              currentWeight: lastWeight,
              change: null,
              changePercentage: null,
              totalChange
            },
            error: null
          }
        }
        return { data: { startWeight: null, currentWeight: null, change: null, changePercentage: null, totalChange: null }, error }
      }

      const startWeight = entries[0].weight_value
      const currentWeight = entries[entries.length - 1].weight_value
      const change = currentWeight - startWeight
      const changePercentage = startWeight > 0 ? (change / startWeight) * 100 : 0

      // Calculate total change from all entries
      const { data: allEntries } = await this.getUserWeightEntries(userId)
      let totalChange = null
      if (allEntries && allEntries.length > 0) {
        const sortedEntries = allEntries.sort((a, b) =>
          new Date(a.recorded_date).getTime() - new Date(b.recorded_date).getTime()
        )
        const firstWeight = sortedEntries[0].weight_value
        const lastWeight = sortedEntries[sortedEntries.length - 1].weight_value
        totalChange = lastWeight - firstWeight
      }

      return {
        data: { startWeight, currentWeight, change, changePercentage, totalChange },
        error: null
      }
    } catch (error) {
      console.error('Error calculating weight progress:', error)
      return { data: null, error }
    }
  }
}
