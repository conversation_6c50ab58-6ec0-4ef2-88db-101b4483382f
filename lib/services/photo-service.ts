import { supabase } from '../supabase'

export interface ProgressPhoto {
  id?: string
  user_id: string
  photo_type: 'front' | 'side' | 'back' | 'progress' | 'other'
  storage_path: string
  thumbnail_path?: string
  original_filename?: string
  file_size?: number
  mime_type?: string
  width?: number
  height?: number
  upload_date: string
  notes?: string
  created_at?: string
  updated_at?: string
}

export class PhotoService {
  private static readonly BUCKET_NAME = 'progress-photos'
  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
  private static readonly ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp']

  /**
   * Upload a progress photo with automatic resizing
   */
  static async uploadPhoto(
    userId: string,
    file: File,
    photoType: 'front' | 'side' | 'back' | 'progress' | 'other',
    uploadDate: string,
    notes?: string
  ): Promise<{ data: ProgressPhoto | null; error: any }> {
    try {
      // Validate file
      if (!this.ALLOWED_TYPES.includes(file.type)) {
        return { data: null, error: { message: 'Invalid file type. Please upload JPEG, PNG, or WebP images.' } }
      }

      if (file.size > this.MAX_FILE_SIZE) {
        return { data: null, error: { message: 'File too large. Maximum size is 10MB.' } }
      }

      // Create unique filename
      const fileExt = file.name.split('.').pop()
      const fileName = `${userId}/${Date.now()}_${photoType}.${fileExt}`
      const thumbnailName = `${userId}/thumbnails/${Date.now()}_${photoType}_thumb.${fileExt}`

      // Resize image for web viewing (800px max width)
      const resizedFile = await this.resizeImage(file, 800)
      const thumbnailFile = await this.resizeImage(file, 150)

      // Upload main image
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, resizedFile)

      if (uploadError) {
        return { data: null, error: uploadError }
      }

      // Upload thumbnail
      const { error: thumbnailError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(thumbnailName, thumbnailFile)

      if (thumbnailError) {
        console.warn('Failed to upload thumbnail:', thumbnailError)
      }

      // Get image dimensions
      const dimensions = await this.getImageDimensions(resizedFile)

      // Save metadata to database
      const photoData: Omit<ProgressPhoto, 'id' | 'created_at' | 'updated_at'> = {
        user_id: userId,
        photo_type: photoType,
        storage_path: fileName,
        thumbnail_path: thumbnailError ? undefined : thumbnailName,
        original_filename: file.name,
        file_size: resizedFile.size,
        mime_type: file.type,
        width: dimensions.width,
        height: dimensions.height,
        upload_date: uploadDate,
        notes: notes
      }

      const { data, error } = await supabase
        .from('progress_photos')
        .insert(photoData)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error uploading photo:', error)
      return { data: null, error }
    }
  }

  /**
   * Get all progress photos for a user
   */
  static async getUserPhotos(userId: string): Promise<{ data: ProgressPhoto[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('progress_photos')
        .select('*')
        .eq('user_id', userId)
        .order('upload_date', { ascending: false })

      return { data, error }
    } catch (error) {
      console.error('Error fetching user photos:', error)
      return { data: null, error }
    }
  }

  /**
   * Get photos by type for a user
   */
  static async getPhotosByType(
    userId: string,
    photoType: 'front' | 'side' | 'back' | 'progress' | 'other'
  ): Promise<{ data: ProgressPhoto[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('progress_photos')
        .select('*')
        .eq('user_id', userId)
        .eq('photo_type', photoType)
        .order('upload_date', { ascending: false })

      return { data, error }
    } catch (error) {
      console.error('Error fetching photos by type:', error)
      return { data: null, error }
    }
  }

  /**
   * Get signed URL for viewing a photo
   */
  static async getPhotoUrl(storagePath: string, expiresIn: number = 3600): Promise<{ data: string | null; error: any }> {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .createSignedUrl(storagePath, expiresIn)

      return { data: data?.signedUrl || null, error }
    } catch (error) {
      console.error('Error getting photo URL:', error)
      return { data: null, error }
    }
  }

  /**
   * Delete a progress photo
   */
  static async deletePhoto(photoId: string): Promise<{ error: any }> {
    try {
      // Get photo metadata first
      const { data: photo, error: fetchError } = await supabase
        .from('progress_photos')
        .select('storage_path, thumbnail_path')
        .eq('id', photoId)
        .single()

      if (fetchError || !photo) {
        return { error: fetchError || { message: 'Photo not found' } }
      }

      // Delete from storage
      const filesToDelete = [photo.storage_path]
      if (photo.thumbnail_path) {
        filesToDelete.push(photo.thumbnail_path)
      }

      const { error: storageError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove(filesToDelete)

      if (storageError) {
        console.warn('Error deleting files from storage:', storageError)
      }

      // Delete from database
      const { error: dbError } = await supabase
        .from('progress_photos')
        .delete()
        .eq('id', photoId)

      return { error: dbError }
    } catch (error) {
      console.error('Error deleting photo:', error)
      return { error }
    }
  }

  /**
   * Resize image to specified max width while maintaining aspect ratio
   */
  private static async resizeImage(file: File, maxWidth: number): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      const img = new Image()

      img.onload = () => {
        const ratio = Math.min(maxWidth / img.width, maxWidth / img.height)
        canvas.width = img.width * ratio
        canvas.height = img.height * ratio

        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)

        canvas.toBlob((blob) => {
          if (blob) {
            const resizedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            })
            resolve(resizedFile)
          } else {
            resolve(file) // Fallback to original file
          }
        }, file.type, 0.9) // 90% quality
      }

      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * Get image dimensions
   */
  private static async getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        resolve({ width: img.width, height: img.height })
      }
      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * Update photo metadata
   */
  static async updatePhoto(photoId: string, updates: Partial<ProgressPhoto>): Promise<{ data: ProgressPhoto | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('progress_photos')
        .update(updates)
        .eq('id', photoId)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error updating photo:', error)
      return { data: null, error }
    }
  }
}
