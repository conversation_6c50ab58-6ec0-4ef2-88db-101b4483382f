import { supabase } from '../supabase'

export interface WorkoutLog {
  id?: string
  user_id: string
  workout_name: string
  workout_date: string
  duration_minutes?: number
  exercises_completed?: number
  total_exercises?: number
  difficulty_rating?: number
  energy_level?: number
  notes?: string
  created_at?: string
  updated_at?: string
}



export class WorkoutService {
  /**
   * Get all workout logs for a user (adapted for actual table structure)
   */
  static async getUserWorkoutLogs(userId: string): Promise<{ data: WorkoutLog[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('workout_logs')
        .select('*')
        .eq('user_id', userId)
        .order('started_at', { ascending: false })

      if (error || !data) {
        return { data: null, error }
      }

      // Adapt the data to match our expected interface
      const adaptedData: WorkoutLog[] = data.map(log => ({
        id: log.id,
        user_id: log.user_id,
        workout_name: log.workout_name_actual || 'Workout',
        workout_date: log.started_at ? new Date(log.started_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        duration_minutes: log.duration_seconds ? Math.round(log.duration_seconds / 60) : 0,
        created_at: log.created_at,
        updated_at: log.updated_at
      }))

      return { data: adaptedData, error: null }
    } catch (error) {
      console.error('Error fetching workout logs:', error)
      return { data: null, error }
    }
  }

  /**
   * Add a new workout log
   */
  static async addWorkoutLog(log: Omit<WorkoutLog, 'id' | 'created_at' | 'updated_at'>): Promise<{ data: WorkoutLog | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('workout_logs')
        .insert(log)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error adding workout log:', error)
      return { data: null, error }
    }
  }

  /**
   * Update a workout log
   */
  static async updateWorkoutLog(id: string, updates: Partial<WorkoutLog>): Promise<{ data: WorkoutLog | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('workout_logs')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error updating workout log:', error)
      return { data: null, error }
    }
  }

  /**
   * Delete a workout log
   */
  static async deleteWorkoutLog(id: string): Promise<{ error: any }> {
    try {
      const { error } = await supabase
        .from('workout_logs')
        .delete()
        .eq('id', id)

      return { error }
    } catch (error) {
      console.error('Error deleting workout log:', error)
      return { error }
    }
  }

  /**
   * Get workout logs for a date range (adapted for actual table structure)
   */
  static async getWorkoutLogsInRange(
    userId: string,
    startDate: string,
    endDate: string
  ): Promise<{ data: WorkoutLog[] | null; error: any }> {
    try {
      // Convert dates to timestamps for comparison with started_at
      const startTimestamp = `${startDate}T00:00:00.000Z`
      const endTimestamp = `${endDate}T23:59:59.999Z`

      const { data, error } = await supabase
        .from('workout_logs')
        .select('*')
        .eq('user_id', userId)
        .gte('started_at', startTimestamp)
        .lte('started_at', endTimestamp)
        .order('started_at', { ascending: true })

      if (error || !data) {
        return { data: null, error }
      }

      // Adapt the data to match our expected interface
      const adaptedData: WorkoutLog[] = data.map(log => ({
        id: log.id,
        user_id: log.user_id,
        workout_name: log.workout_name_actual || 'Workout',
        workout_date: log.started_at ? new Date(log.started_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        duration_minutes: log.duration_seconds ? Math.round(log.duration_seconds / 60) : 0,
        created_at: log.created_at,
        updated_at: log.updated_at
      }))

      return { data: adaptedData, error: null }
    } catch (error) {
      console.error('Error fetching workout logs in range:', error)
      return { data: null, error }
    }
  }

  /**
   * Get workout statistics
   */
  static async getWorkoutStats(userId: string, days: number = 30): Promise<{
    data: {
      totalWorkouts: number;
      totalMinutes: number;
      averageDuration: number;
      averageDifficulty: number;
      averageEnergy: number;
      streak: number;
      weeklyWorkouts: number;
    } | null;
    error: any
  }> {
    try {
      const endDate = new Date().toISOString().split('T')[0]
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

      const { data: workouts, error } = await this.getWorkoutLogsInRange(userId, startDate, endDate)
      
      if (error || !workouts) {
        return { data: null, error }
      }

      const totalWorkouts = workouts.length
      const totalMinutes = workouts.reduce((sum, w) => sum + (w.duration_minutes || 0), 0)
      const averageDuration = totalWorkouts > 0 ? totalMinutes / totalWorkouts : 0
      
      const ratingsWithDifficulty = workouts.filter(w => w.difficulty_rating)
      const averageDifficulty = ratingsWithDifficulty.length > 0 
        ? ratingsWithDifficulty.reduce((sum, w) => sum + (w.difficulty_rating || 0), 0) / ratingsWithDifficulty.length 
        : 0

      const ratingsWithEnergy = workouts.filter(w => w.energy_level)
      const averageEnergy = ratingsWithEnergy.length > 0 
        ? ratingsWithEnergy.reduce((sum, w) => sum + (w.energy_level || 0), 0) / ratingsWithEnergy.length 
        : 0

      // Calculate current streak
      const streak = await this.calculateWorkoutStreak(userId)

      // Calculate weekly workouts (current week)
      const weeklyWorkouts = await this.getWeeklyWorkoutCount(userId)

      return {
        data: {
          totalWorkouts,
          totalMinutes,
          averageDuration,
          averageDifficulty,
          averageEnergy,
          streak,
          weeklyWorkouts
        },
        error: null
      }
    } catch (error) {
      console.error('Error calculating workout stats:', error)
      return { data: null, error }
    }
  }

  /**
   * Get workout count for current week (Monday to Sunday)
   */
  static async getWeeklyWorkoutCount(userId: string): Promise<number> {
    try {
      const today = new Date()
      const currentDay = today.getDay() // 0 = Sunday, 1 = Monday, etc.
      const mondayOffset = currentDay === 0 ? 6 : currentDay - 1 // Calculate days since Monday

      // Get Monday of current week
      const monday = new Date(today)
      monday.setDate(today.getDate() - mondayOffset)
      monday.setHours(0, 0, 0, 0)

      // Get Sunday of current week
      const sunday = new Date(monday)
      sunday.setDate(monday.getDate() + 6)
      sunday.setHours(23, 59, 59, 999)

      const startDate = monday.toISOString().split('T')[0]
      const endDate = sunday.toISOString().split('T')[0]

      const { data: workouts, error } = await this.getWorkoutLogsInRange(userId, startDate, endDate)

      if (error || !workouts) {
        return 0
      }

      return workouts.length
    } catch (error) {
      console.error('Error calculating weekly workout count:', error)
      return 0
    }
  }

  /**
   * Calculate current workout streak (adapted for actual table structure)
   */
  static async calculateWorkoutStreak(userId: string): Promise<number> {
    try {
      const { data: workouts, error } = await supabase
        .from('workout_logs')
        .select('started_at')
        .eq('user_id', userId)
        .order('started_at', { ascending: false })

      if (error || !workouts || workouts.length === 0) {
        return 0
      }

      let streak = 0
      const today = new Date()
      const workoutDates = workouts.map(w => new Date(w.started_at).toISOString().split('T')[0]).map(d => new Date(d))

      // Remove duplicates (same day workouts)
      const uniqueDates = Array.from(new Set(workoutDates.map(d => d.toDateString()))).map(d => new Date(d))
      uniqueDates.sort((a, b) => b.getTime() - a.getTime()) // Sort descending

      if (uniqueDates.length === 0) return 0

      // Check if there's a workout today or yesterday
      const latestWorkout = uniqueDates[0]
      const daysDiff = Math.floor((today.getTime() - latestWorkout.getTime()) / (1000 * 60 * 60 * 24))

      if (daysDiff > 1) {
        return 0 // Streak broken
      }

      // Count consecutive days with workouts
      let currentDate = new Date(latestWorkout)
      for (const workoutDate of uniqueDates) {
        const diff = Math.floor((currentDate.getTime() - workoutDate.getTime()) / (1000 * 60 * 60 * 24))
        if (diff <= 1) {
          streak++
          currentDate = new Date(workoutDate)
        } else {
          break
        }
      }

      return streak
    } catch (error) {
      console.error('Error calculating workout streak:', error)
      return 0
    }
  }


}
