import { supabase } from './supabase'

export interface IntakeResponse {
  id?: string
  user_id: string
  has_limitations: boolean
  location: string
  goal: string
  experience: string
  wants_mobility: boolean
  flex_score?: number
  mobility_add_on: string
  program_id: string
  program_name: string
  completion_status: 'complete' | 'incomplete'
  created_at?: string
  updated_at?: string
}

export class IntakeService {
  /**
   * Save intake form responses to the database
   */
  static async saveIntakeResponse(
    userId: string,
    answers: Record<string, any>,
    programId: string,
    programName: string
  ): Promise<{ data: IntakeResponse | null; error: any }> {
    try {
      const intakeData: Omit<IntakeResponse, 'id' | 'created_at' | 'updated_at'> = {
        user_id: userId,
        has_limitations: answers.has_limitations || false,
        location: answers.location as string,
        goal: answers.goal as string,
        experience: answers.experience as string,
        wants_mobility: answers.wants_mobility || false,
        flex_score: answers.flex_score || null,
        mobility_add_on: answers.mobility_add_on || 'none',
        program_id: programId,
        program_name: programName,
        completion_status: 'complete'
      }

      const { data, error } = await supabase
        .from('intake_responses')
        .insert(intakeData)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error saving intake response:', error)
      return { data: null, error }
    }
  }

  /**
   * Get the latest intake response for a user
   */
  static async getLatestIntakeResponse(userId: string): Promise<{ data: IntakeResponse | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('intake_responses')
        .select('*')
        .eq('user_id', userId)
        .eq('completion_status', 'complete')
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error fetching intake response:', error)
      return { data: null, error }
    }
  }

  /**
   * Check if user has completed intake
   */
  static async hasCompletedIntake(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('intake_responses')
        .select('id')
        .eq('user_id', userId)
        .eq('completion_status', 'complete')
        .limit(1)

      if (error) {
        console.error('Error checking intake completion:', error)
        return false
      }

      return data && data.length > 0
    } catch (error) {
      console.error('Error checking intake completion:', error)
      return false
    }
  }

  /**
   * Update an existing intake response
   */
  static async updateIntakeResponse(
    userId: string,
    answers: Record<string, any>,
    programId: string,
    programName: string
  ): Promise<{ data: IntakeResponse | null; error: any }> {
    try {
      const updateData = {
        has_limitations: answers.has_limitations || false,
        location: answers.location as string,
        goal: answers.goal as string,
        experience: answers.experience as string,
        wants_mobility: answers.wants_mobility || false,
        flex_score: answers.flex_score || null,
        mobility_add_on: answers.mobility_add_on || 'none',
        program_id: programId,
        program_name: programName,
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('intake_responses')
        .update(updateData)
        .eq('user_id', userId)
        .select()
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error updating intake response:', error)
      return { data: null, error }
    }
  }
}
