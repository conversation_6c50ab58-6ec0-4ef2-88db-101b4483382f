import { supabase } from './supabase'

export interface IntakeResponse {
  id?: string
  user_id: string
  goals: string[]
  equipment: string
  experience: string
  frequency: string
  mobility_concerns: string[]
  program_name: string
  completion_status: 'complete' | 'incomplete'
  created_at?: string
  updated_at?: string
}

export class IntakeService {
  /**
   * Save intake form responses to the database
   */
  static async saveIntakeResponse(
    userId: string,
    answers: Record<string, string | string[]>,
    programName: string
  ): Promise<{ data: IntakeResponse | null; error: any }> {
    try {
      const intakeData: Omit<IntakeResponse, 'id' | 'created_at' | 'updated_at'> = {
        user_id: userId,
        goals: Array.isArray(answers.goals) ? answers.goals : [],
        equipment: answers.equipment as string,
        experience: answers.experience as string,
        frequency: answers.frequency as string,
        mobility_concerns: Array.isArray(answers.mobility) ? answers.mobility : [],
        program_name: programName,
        completion_status: 'complete'
      }

      const { data, error } = await supabase
        .from('intake_responses')
        .insert(intakeData)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error saving intake response:', error)
      return { data: null, error }
    }
  }

  /**
   * Get the latest intake response for a user
   */
  static async getLatestIntakeResponse(userId: string): Promise<{ data: IntakeResponse | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('intake_responses')
        .select('*')
        .eq('user_id', userId)
        .eq('completion_status', 'complete')
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error fetching intake response:', error)
      return { data: null, error }
    }
  }

  /**
   * Check if user has completed intake
   */
  static async hasCompletedIntake(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('intake_responses')
        .select('id')
        .eq('user_id', userId)
        .eq('completion_status', 'complete')
        .limit(1)

      if (error) {
        console.error('Error checking intake completion:', error)
        return false
      }

      return data && data.length > 0
    } catch (error) {
      console.error('Error checking intake completion:', error)
      return false
    }
  }

  /**
   * Update an existing intake response
   */
  static async updateIntakeResponse(
    userId: string,
    answers: Record<string, string | string[]>,
    programName: string
  ): Promise<{ data: IntakeResponse | null; error: any }> {
    try {
      const updateData = {
        goals: Array.isArray(answers.goals) ? answers.goals : [],
        equipment: answers.equipment as string,
        experience: answers.experience as string,
        frequency: answers.frequency as string,
        mobility_concerns: Array.isArray(answers.mobility) ? answers.mobility : [],
        program_name: programName,
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('intake_responses')
        .update(updateData)
        .eq('user_id', userId)
        .select()
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      return { data, error }
    } catch (error) {
      console.error('Error updating intake response:', error)
      return { data: null, error }
    }
  }
}
