// User and Authentication Types
export interface User {
  firstName: string
  lastName: string
  email: string
  password?: string // Optional for security - shouldn't be stored in state
  program?: string
  id?: string
  createdAt?: Date
}

export interface SignupFormData {
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
}

// Workout and Exercise Types
export interface Exercise {
  name: string
  sets: string
  reps: string
  rest: string
  muscle: string
}

export interface Workout {
  day: string
  description: string
  exercises: Exercise[]
}

export interface WorkoutProgram {
  description: string
  schedule: string
  duration: string
  difficulty: string
  workouts: Workout[]
}

// Progress and Metrics Types
export interface ProgressDataPoint {
  date: string
  value: number
}



// Component Props Types
export interface DashboardProps {
  user: User
}

export interface WorkoutSessionProps {
  workout: Workout
  onComplete: () => void
  onExit: () => void
}



export interface ProgressDashboardProps {
  user: User
}

export interface ProgressCheckinDashboardProps {
  user: User
}



export interface QuestionnaireProps {
  onComplete: (program: string) => void
}

// Form and UI Types
export interface FormErrors {
  [key: string]: string
}

export type WorkoutDifficulty = 'too-easy' | 'just-right' | 'too-hard'

export type CurrentStep = 'signup' | 'questionnaire' | 'dashboard'

// Error Handling Types
export interface AppError {
  message: string
  type: 'validation' | 'storage' | 'network' | 'general'
  field?: string
}

export interface ErrorState {
  hasError: boolean
  error: AppError | null
}

// Password validation types
export interface PasswordValidationResult {
  isValid: boolean
  errors: string[]
  strength: 'weak' | 'medium' | 'strong'
}

// App State Management Types
export interface AppState {
  user: User | null
  currentStep: CurrentStep
  errorState: ErrorState
  progressData: ProgressData
  workoutData: WorkoutData
}

export interface ProgressData {
  weight: ProgressDataPoint[]
  workouts: WorkoutProgressPoint[]
}

export interface WorkoutProgressPoint {
  date: string
  workouts: number
  target: number
}



export interface WorkoutData {
  currentProgram: string | null
  completedWorkouts: string[]
  currentWorkout: Workout | null
  workoutHistory: WorkoutHistoryEntry[]
}

export interface WorkoutHistoryEntry {
  date: string
  workoutName: string
  exercises: ExerciseResult[]
  duration: number
}

export interface ExerciseResult {
  name: string
  sets: SetResult[]
}

export interface SetResult {
  reps: number
  weight?: number
  completed: boolean
}
