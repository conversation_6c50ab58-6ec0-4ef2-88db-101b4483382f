import { AppE<PERSON><PERSON><PERSON><PERSON><PERSON> } from './error-handler'

/**
 * Secure storage utility that provides encryption and validation for client-side data storage
 * Note: This is still client-side storage and should not be used for truly sensitive data
 * For production apps, sensitive data should be stored server-side with proper authentication
 */
export class SecureStorage {
  private static readonly STORAGE_KEY_PREFIX = 'fitness_app_'
  private static readonly ENCRYPTION_KEY = 'fitness_app_key_2024' // In production, this should be environment-specific

  /**
   * Simple XOR encryption for basic obfuscation
   * Note: This is NOT cryptographically secure and is only for basic obfuscation
   * In production, use proper encryption libraries like crypto-js
   */
  private static simpleEncrypt(text: string, key: string): string {
    let result = ''
    for (let i = 0; i < text.length; i++) {
      result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length))
    }
    return btoa(result) // Base64 encode
  }

  private static simpleDecrypt(encryptedText: string, key: string): string {
    try {
      const decoded = atob(encryptedText) // Base64 decode
      let result = ''
      for (let i = 0; i < decoded.length; i++) {
        result += String.fromCharCode(decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length))
      }
      return result
    } catch {
      throw new Error('Failed to decrypt data')
    }
  }

  /**
   * Securely store data with encryption and integrity checking
   */
  static setItem(key: string, value: any): void {
    try {
      const data = {
        value,
        timestamp: Date.now(),
        checksum: this.generateChecksum(JSON.stringify(value))
      }
      
      const jsonString = JSON.stringify(data)
      const encrypted = this.simpleEncrypt(jsonString, this.ENCRYPTION_KEY)
      const storageKey = this.STORAGE_KEY_PREFIX + key
      
      localStorage.setItem(storageKey, encrypted)
    } catch (error) {
      throw AppErrorHandler.handleStorageError(error)
    }
  }

  /**
   * Securely retrieve and decrypt data with integrity verification
   */
  static getItem<T>(key: string): T | null {
    try {
      const storageKey = this.STORAGE_KEY_PREFIX + key
      const encrypted = localStorage.getItem(storageKey)
      
      if (!encrypted) {
        return null
      }

      const decrypted = this.simpleDecrypt(encrypted, this.ENCRYPTION_KEY)
      const data = JSON.parse(decrypted)

      // Verify data integrity
      if (!this.verifyChecksum(JSON.stringify(data.value), data.checksum)) {
        throw new Error('Data integrity check failed')
      }

      // Check if data is expired (optional - 30 days)
      const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000
      if (Date.now() - data.timestamp > thirtyDaysInMs) {
        this.removeItem(key)
        return null
      }

      return data.value
    } catch (error) {
      // If decryption fails, remove corrupted data
      this.removeItem(key)
      throw AppErrorHandler.handleStorageError(error)
    }
  }

  /**
   * Remove item from secure storage
   */
  static removeItem(key: string): void {
    try {
      const storageKey = this.STORAGE_KEY_PREFIX + key
      localStorage.removeItem(storageKey)
    } catch (error) {
      throw AppErrorHandler.handleStorageError(error)
    }
  }

  /**
   * Clear all app data from storage
   */
  static clear(): void {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(this.STORAGE_KEY_PREFIX)) {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      throw AppErrorHandler.handleStorageError(error)
    }
  }

  /**
   * Check if storage is available
   */
  static isAvailable(): boolean {
    try {
      const testKey = '__storage_test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      return true
    } catch {
      return false
    }
  }

  /**
   * Generate simple checksum for data integrity
   */
  private static generateChecksum(data: string): string {
    let hash = 0
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString()
  }

  /**
   * Verify data integrity using checksum
   */
  private static verifyChecksum(data: string, expectedChecksum: string): boolean {
    return this.generateChecksum(data) === expectedChecksum
  }

  /**
   * Get storage usage information
   */
  static getStorageInfo(): { used: number; available: number; percentage: number } {
    try {
      let used = 0
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(this.STORAGE_KEY_PREFIX)) {
          used += localStorage.getItem(key)?.length || 0
        }
      })

      // Estimate available storage (5MB typical limit)
      const estimated = 5 * 1024 * 1024 // 5MB in bytes
      const percentage = (used / estimated) * 100

      return {
        used,
        available: estimated - used,
        percentage: Math.min(percentage, 100)
      }
    } catch {
      return { used: 0, available: 0, percentage: 0 }
    }
  }
}
