import { ProgressData, WorkoutData, ProgressDataPoint, WorkoutProgressPoint, CheckInData } from './types'

/**
 * Data service that provides mock data for the application
 * This can be easily replaced with real API calls in the future
 */
export class DataService {
  
  /**
   * Get initial progress data for a user
   */
  static getInitialProgressData(): ProgressData {
    return {
      weight: this.getMockWeightData(),
      workouts: this.getMockWorkoutProgressData()
    }
  }

  /**
   * Get initial workout data for a user
   */
  static getInitialWorkoutData(): WorkoutData {
    return {
      currentProgram: null,
      completedWorkouts: [],
      currentWorkout: null,
      workoutHistory: []
    }
  }

  /**
   * Generate mock weight data
   */
  private static getMockWeightData(): ProgressDataPoint[] {
    const baseWeight = 185
    const data: ProgressDataPoint[] = []
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 60) // 60 days ago

    for (let i = 0; i < 9; i++) {
      const date = new Date(startDate)
      date.setDate(date.getDate() + (i * 7)) // Weekly data points
      
      // Simulate gradual weight loss with some variation
      const weightLoss = (i * 0.5) + (Math.random() - 0.5) * 2
      const weight = baseWeight - weightLoss

      data.push({
        date: date.toISOString().split('T')[0],
        value: Math.round(weight * 10) / 10 // Round to 1 decimal
      })
    }

    return data
  }

  /**
   * Generate mock workout progress data
   */
  private static getMockWorkoutProgressData(): WorkoutProgressPoint[] {
    const data: WorkoutProgressPoint[] = []
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 42) // 6 weeks ago

    for (let i = 0; i < 6; i++) {
      const date = new Date(startDate)
      date.setDate(date.getDate() + (i * 7)) // Weekly data points
      
      // Simulate workout completion with some variation
      const target = 4
      const completed = Math.min(target, Math.max(2, target + Math.floor((Math.random() - 0.3) * 2)))

      data.push({
        date: `Week ${i + 1}`,
        workouts: completed,
        target: target
      })
    }

    return data
  }



  /**
   * Simulate adding new progress data
   */
  static async addProgressData(type: 'weight' | 'workout', data: any): Promise<boolean> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))

    // Simulate 95% success rate
    return Math.random() > 0.05
  }

  /**
   * Get user statistics
   */
  static getUserStats(progressData: ProgressData) {
    const weightData = progressData.weight
    const workoutData = progressData.workouts
    
    const currentWeight = weightData.length > 0 ? weightData[weightData.length - 1].value : 0
    const startWeight = weightData.length > 0 ? weightData[0].value : 0
    const weightLoss = startWeight - currentWeight

    const totalWorkouts = workoutData.reduce((sum, week) => sum + week.workouts, 0)
    const weeklyAverage = workoutData.length > 0 ? totalWorkouts / workoutData.length : 0
    
    // Calculate current streak (simplified)
    const currentStreak = Math.floor(Math.random() * 15) + 5 // 5-19 days

    return {
      weeklyWorkouts: Math.round(weeklyAverage),
      totalWorkouts,
      currentStreak,
      weightLoss: Math.round(weightLoss * 10) / 10
    }
  }

  /**
   * Check if user should see initial data setup
   */
  static shouldShowInitialData(progressData: ProgressData): boolean {
    return progressData.weight.length === 0 && 
           progressData.workouts.length === 0 && 
           progressData.checkIns.length === 0
  }
}
