import { PasswordValidationResult } from './types'

export class PasswordValidator {
  private static readonly MIN_LENGTH = 8
  private static readonly MAX_LENGTH = 128

  static validate(password: string): PasswordValidationResult {
    const errors: string[] = []
    let strength: 'weak' | 'medium' | 'strong' = 'weak'

    // Length validation
    if (password.length < this.MIN_LENGTH) {
      errors.push(`Password must be at least ${this.MIN_LENGTH} characters long`)
    }
    if (password.length > this.MAX_LENGTH) {
      errors.push(`Password must be no more than ${this.MAX_LENGTH} characters long`)
    }

    // Character type validation
    const hasUpperCase = /[A-Z]/.test(password)
    const hasLowerCase = /[a-z]/.test(password)
    const hasNumbers = /\d/.test(password)
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password)

    if (!hasUpperCase) {
      errors.push('Password must contain at least one uppercase letter')
    }
    if (!hasLowerCase) {
      errors.push('Password must contain at least one lowercase letter')
    }
    if (!hasNumbers) {
      errors.push('Password must contain at least one number')
    }
    if (!hasSpecialChar) {
      errors.push('Password must contain at least one special character (!@#$%^&*(),.?":{}|<>)')
    }

    // Common password patterns to avoid
    const commonPatterns = [
      /(.)\1{2,}/, // Three or more repeated characters
      /123456|654321|abcdef|qwerty|password/i, // Common sequences
    ]

    for (const pattern of commonPatterns) {
      if (pattern.test(password)) {
        errors.push('Password contains common patterns that are easily guessed')
        break
      }
    }

    // Calculate strength
    const criteriaCount = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChar].filter(Boolean).length
    const lengthBonus = password.length >= 12 ? 1 : 0
    const totalScore = criteriaCount + lengthBonus

    if (totalScore >= 5 && password.length >= 12) {
      strength = 'strong'
    } else if (totalScore >= 3 && password.length >= 8) {
      strength = 'medium'
    } else {
      strength = 'weak'
    }

    return {
      isValid: errors.length === 0,
      errors,
      strength
    }
  }

  static getStrengthColor(strength: 'weak' | 'medium' | 'strong'): string {
    switch (strength) {
      case 'weak':
        return 'text-red-600'
      case 'medium':
        return 'text-yellow-600'
      case 'strong':
        return 'text-green-600'
      default:
        return 'text-gray-600'
    }
  }

  static getStrengthText(strength: 'weak' | 'medium' | 'strong'): string {
    switch (strength) {
      case 'weak':
        return 'Weak'
      case 'medium':
        return 'Medium'
      case 'strong':
        return 'Strong'
      default:
        return 'Unknown'
    }
  }

  static getPasswordRequirements(): string[] {
    return [
      `At least ${this.MIN_LENGTH} characters long`,
      'At least one uppercase letter (A-Z)',
      'At least one lowercase letter (a-z)',
      'At least one number (0-9)',
      'At least one special character (!@#$%^&*(),.?":{}|<>)',
      'Avoid common patterns and repeated characters'
    ]
  }
}
